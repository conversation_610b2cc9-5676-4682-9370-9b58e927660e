#!/usr/bin/env python3
"""
简化的批量处理测试脚本

验证VideoPreprocessor的核心批量处理功能
"""

import sys
import os
import tempfile
from unittest.mock import patch, MagicMock

# 添加父目录到Python路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

from dms_automation_pipeline.core.video_preprocessor import VideoPreprocessor, VideoProcessingResult


def create_test_config(output_dir: str) -> dict:
    """创建测试配置"""
    return {
        "video_processing": {
            "input_directory": "./input_videos/",
            "output_directory": output_dir,
            "supported_formats": [".mkv", ".mp4", ".avi"],
            "deduplication": {
                "enabled": True,
                "force_reprocess": False,
                "naming_pattern": "{base_name}_{time_range}_roi_{roi}.mp4"
            },
            "batch_processing": {
                "max_parallel": 2,
                "retry_count": 1,
                "timeout_seconds": 30
            }
        }
    }


def test_batch_processing_mock():
    """测试批量处理功能（使用Mock）"""
    print("=== 测试批量处理功能（Mock版本） ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config = create_test_config(temp_dir)
        processor = VideoPreprocessor(config)
        
        # 创建测试视频文件
        video1_path = os.path.join(temp_dir, "video1.mkv")
        video2_path = os.path.join(temp_dir, "video2.mkv")
        
        with open(video1_path, 'w') as f:
            f.write("fake video content")
        with open(video2_path, 'w') as f:
            f.write("fake video content")
        
        # Mock所有依赖
        with patch.object(processor, 'validate_inputs') as mock_validate, \
             patch.object(processor, 'process_single_segment') as mock_process:
            
            # 设置Mock返回值
            mock_validate.return_value = (True, "")
            
            # 模拟成功的处理结果
            def mock_segment_processing(video_path, start_time, end_time, roi=None):
                result = VideoProcessingResult(
                    success=True,
                    message=f"成功处理片段: {start_time}-{end_time}",
                    output_files=[f"output_{os.path.basename(video_path)}_{start_time}_{end_time}.mp4"]
                )
                result.processing_time = 0.1
                return result
            
            mock_process.side_effect = mock_segment_processing
            
            # 准备批量配置
            batch_config = [
                {
                    "path": video1_path,
                    "time_ranges": [("00:04:57", "00:05:17")],
                    "roi": "1920:1080:0:0"
                },
                {
                    "path": video2_path,
                    "time_ranges": [("00:01:00", "00:01:20"), ("00:02:00", "00:02:20")],
                    "roi": "1280:720:0:0"
                }
            ]
            
            # 执行批量处理
            result = processor.process_batch(batch_config)
            
            print(f"✓ 批量处理结果: {result.success}")
            print(f"✓ 处理消息: {result.message}")
            print(f"✓ 输出文件数: {len(result.output_files)}")
            print(f"✓ 处理时间: {result.processing_time:.2f}秒")
            
            assert result.success == True
            assert len(result.output_files) == 3  # 总共3个片段
            assert result.processing_time > 0
            
            # 验证process_single_segment被调用了正确的次数
            assert mock_process.call_count == 3
            
            print("✓ 批量处理功能测试通过")
    
    return True


def test_batch_processing_with_failures_mock():
    """测试带失败的批量处理（Mock版本）"""
    print("\n=== 测试带失败的批量处理（Mock版本） ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config = create_test_config(temp_dir)
        processor = VideoPreprocessor(config)
        
        # 创建测试视频文件
        video_paths = []
        for i in range(1, 4):
            video_path = os.path.join(temp_dir, f"video{i}.mkv")
            with open(video_path, 'w') as f:
                f.write("fake video content")
            video_paths.append(video_path)
        
        with patch.object(processor, 'validate_inputs') as mock_validate, \
             patch.object(processor, 'process_single_segment') as mock_process:
            
            mock_validate.return_value = (True, "")
            
            # 模拟部分失败的处理结果
            call_count = 0
            def mock_segment_with_failures(video_path, start_time, end_time, roi=None):
                nonlocal call_count
                call_count += 1
                
                if call_count == 2:  # 第二次调用失败
                    result = VideoProcessingResult(
                        success=False,
                        message=f"处理失败: {start_time}-{end_time}",
                        error="模拟处理失败"
                    )
                else:
                    result = VideoProcessingResult(
                        success=True,
                        message=f"成功处理片段: {start_time}-{end_time}",
                        output_files=[f"output_{os.path.basename(video_path)}_{start_time}_{end_time}.mp4"]
                    )
                
                result.processing_time = 0.1
                return result
            
            mock_process.side_effect = mock_segment_with_failures
            
            batch_config = [
                {
                    "path": video_paths[0],
                    "time_ranges": [("00:04:57", "00:05:17")],
                    "roi": "1920:1080:0:0"
                },
                {
                    "path": video_paths[1],
                    "time_ranges": [("00:01:00", "00:01:20")],  # 这个会失败
                    "roi": "1280:720:0:0"
                },
                {
                    "path": video_paths[2],
                    "time_ranges": [("00:02:00", "00:02:20")],
                    "roi": "1920:1080:0:0"
                }
            ]
            
            result = processor.process_batch(batch_config)
            
            print(f"✓ 部分失败结果: {result.success}")
            print(f"✓ 处理消息: {result.message}")
            print(f"✓ 成功文件数: {len(result.output_files)}")
            print(f"✓ 错误信息存在: {result.error is not None}")
            
            assert result.success == True  # 部分成功也算成功
            assert len(result.output_files) == 2  # 2个成功，1个失败
            assert result.error is not None  # 应该有错误信息
            
            print("✓ 带失败的批量处理测试通过")
    
    return True


def test_batch_processing_statistics():
    """测试处理统计信息"""
    print("\n=== 测试处理统计信息 ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config = create_test_config(temp_dir)
        processor = VideoPreprocessor(config)
        
        # 创建一些测试文件
        test_files = ["test1.mp4", "test2.mkv", "test3.avi"]
        for filename in test_files:
            file_path = os.path.join(temp_dir, filename)
            with open(file_path, 'w') as f:
                f.write("test content" * 100)  # 创建一些内容
        
        # 获取统计信息
        stats = processor.get_processing_statistics()
        
        print(f"✓ 输出目录: {stats['output_directory']}")
        print(f"✓ 文件总数: {stats['total_files']}")
        print(f"✓ 总大小: {stats['total_size_mb']:.2f} MB")
        print(f"✓ 支持格式: {stats['supported_formats']}")
        print(f"✓ 去重启用: {stats['deduplication_enabled']}")
        print(f"✓ 最大并行数: {stats['max_parallel']}")
        
        assert stats['output_directory'] == temp_dir
        assert stats['total_files'] == len(test_files)
        assert stats['total_size_mb'] > 0
        assert stats['deduplication_enabled'] == True
        assert stats['max_parallel'] == 2
        
        print("✓ 处理统计信息测试通过")
    
    return True


def main():
    """主测试函数"""
    try:
        print("开始测试批量处理功能...")
        
        # 运行所有测试
        tests = [
            test_batch_processing_mock,
            test_batch_processing_with_failures_mock,
            test_batch_processing_statistics
        ]
        
        for test in tests:
            if not test():
                print(f"❌ 测试失败: {test.__name__}")
                return False
                
        print("\n🎉 所有批量处理测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
