{"project_info": {"name": "DMS自动化流水线测试", "version": "1.0.0", "description": "端到端DMS分析自动化系统测试配置"}, "video_processing": {"enabled": true, "input_directory": "./test_input_videos/", "output_directory": "./test_output_videos/", "supported_formats": [".mkv", ".mp4", ".avi"], "ffmpeg_path": "ffmpeg", "roi_default": "1920:1080:0:0", "time_ranges": [{"start": "00:04:57", "end": "00:05:17", "description": "测试片段"}], "deduplication": {"enabled": true, "force_reprocess": false, "naming_pattern": "{base_name}_{time_range}_roi_{roi}.mp4"}, "batch_processing": {"max_parallel": 2, "retry_count": 1, "timeout_seconds": 30}}, "environment": {"enabled": true, "cpp_source_directory": "../BYD_HKH_R_2.01.07.2025.07.08.4_x86/", "runtime_directory": "./runtime_env/", "required_files": ["test_dms_internal_postmortem", "libtx_dms.so", "FaceDetection.ovm", "FaceKeypoints.ovm", "eye.ovm"], "config_files": ["ip_port.json", "calidata.json"], "confirmation": {"timeout_seconds": 10, "default_action": "confirm", "show_rich_display": true}}, "remote": {"enabled": true, "ssh": {"host": "***********", "port": 22, "username": "user", "key_file": "~/.ssh/id_rsa", "timeout": 30, "retry_count": 3}, "service": {"path": "/userfs/tx_dms_oax_test_tool_update", "port": 1180, "start_command": "cd /userfs && ./tx_dms_oax_test_tool_update", "check_interval": 5, "max_wait_time": 60}}, "rendering": {"enabled": true, "cpp_program": "./runtime_env/test_dms_internal_postmortem", "input_format": "frames", "output_format": "video", "output_directory": "./output/", "temp_directory": "./temp_processing/"}, "logging": {"level": "INFO", "console_output": true, "file_output": true, "log_file": "./logs/dms_automation_test.log", "rich_formatting": true}, "pipeline": {"auto_resume": true, "save_state": true, "state_file": ".pipeline_state.json", "progress_display": true, "stages": ["VIDEO_PROCESSING", "ENV_SETUP", "REMOTE_VALIDATION", "RENDERING"]}}