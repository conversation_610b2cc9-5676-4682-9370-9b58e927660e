#!/usr/bin/env python3
"""
DMS自动化分析与渲染平台 - 简化演示脚本

展示平台的核心功能，无需复杂的导入
"""

import os
import sys
import time
import json
from pathlib import Path


def create_demo_files():
    """创建演示文件"""
    print("🎬 创建演示文件...")
    
    # 创建目录
    directories = [
        "input_videos", "cropped_videos", "runtime_env", 
        "output", "results", "logs", "demo_cpp"
    ]
    
    for dir_name in directories:
        Path(dir_name).mkdir(exist_ok=True)
    
    # 创建演示视频文件
    for i in range(3):
        video_path = Path("input_videos") / f"demo_video_{i+1}.mp4"
        with open(video_path, 'w') as f:
            f.write(f"# 演示视频文件 {i+1}\n")
            f.write(f"# 模拟视频内容，大小: {(i+1)*10}MB\n")
    
    # 创建演示C++文件
    cpp_files = ["demo_dms_program", "demo_lib.so", "demo_model.ovm"]
    for file_name in cpp_files:
        file_path = Path("demo_cpp") / file_name
        with open(file_path, 'w') as f:
            f.write(f"# 演示用的 {file_name}\n")
        if not file_name.endswith(('.so', '.ovm')):
            os.chmod(file_path, 0o755)
    
    # 创建配置文件
    ip_port_config = {"ip": "127.0.0.1", "port": 8080}
    with open("runtime_env/ip_port.json", 'w') as f:
        json.dump(ip_port_config, f, indent=2)
    
    calidata_config = {
        "camera_matrix": [[1000, 0, 320], [0, 1000, 240], [0, 0, 1]],
        "distortion": [0.1, -0.2, 0.0, 0.0, 0.0]
    }
    with open("runtime_env/calidata.json", 'w') as f:
        json.dump(calidata_config, f, indent=2)
    
    print("✅ 演示文件创建完成")


def simulate_video_processing():
    """模拟视频处理阶段"""
    print("\n🎬 视频预处理阶段")
    print("-" * 40)
    
    input_videos = list(Path("input_videos").glob("*.mp4"))
    print(f"📁 发现 {len(input_videos)} 个输入视频")
    
    for i, video in enumerate(input_videos, 1):
        print(f"⏳ 处理视频 {i}/{len(input_videos)}: {video.name}")
        time.sleep(0.5)  # 模拟处理时间
        
        # 创建输出文件
        output_path = Path("cropped_videos") / f"cropped_{video.name}"
        with open(output_path, 'w') as f:
            f.write(f"# 裁剪后的视频: {video.name}\n")
            f.write(f"# 处理时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        
        print(f"✅ 完成: {output_path.name}")
    
    print(f"🎉 视频预处理完成: 处理了 {len(input_videos)} 个视频")
    return True


def simulate_environment_setup():
    """模拟环境设置阶段"""
    print("\n🔧 环境管理阶段")
    print("-" * 40)
    
    cpp_files = list(Path("demo_cpp").glob("*"))
    print(f"📁 发现 {len(cpp_files)} 个C++文件")
    
    deployed_count = 0
    for cpp_file in cpp_files:
        print(f"⏳ 部署文件: {cpp_file.name}")
        time.sleep(0.3)  # 模拟部署时间
        
        # 复制到运行时目录
        dest_path = Path("runtime_env") / cpp_file.name
        with open(dest_path, 'w') as f:
            f.write(f"# 部署的文件: {cpp_file.name}\n")
            f.write(f"# 部署时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        
        if not cpp_file.name.endswith(('.so', '.ovm')):
            os.chmod(dest_path, 0o755)
        
        deployed_count += 1
        print(f"✅ 已部署: {cpp_file.name}")
    
    # 验证配置文件
    config_files = ["ip_port.json", "calidata.json"]
    for config_file in config_files:
        config_path = Path("runtime_env") / config_file
        if config_path.exists():
            print(f"✅ 配置文件验证通过: {config_file}")
        else:
            print(f"❌ 配置文件缺失: {config_file}")
    
    print(f"🎉 环境设置完成: 部署了 {deployed_count} 个文件")
    return True


def simulate_rendering():
    """模拟渲染阶段"""
    print("\n🎨 渲染执行阶段")
    print("-" * 40)
    
    cropped_videos = list(Path("cropped_videos").glob("*.mp4"))
    print(f"📁 发现 {len(cropped_videos)} 个待渲染视频")
    
    rendered_count = 0
    failed_count = 0
    
    for i, video in enumerate(cropped_videos, 1):
        print(f"⏳ 渲染视频 {i}/{len(cropped_videos)}: {video.name}")
        time.sleep(0.8)  # 模拟渲染时间
        
        # 模拟渲染成功/失败
        import random
        if random.random() > 0.1:  # 90% 成功率
            output_path = Path("output") / f"rendered_{video.name}"
            with open(output_path, 'w') as f:
                f.write(f"# 渲染结果: {video.name}\n")
                f.write(f"# 渲染时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"# 文件大小: {random.randint(50, 200)}MB\n")
            
            rendered_count += 1
            print(f"✅ 渲染完成: {output_path.name}")
        else:
            failed_count += 1
            print(f"❌ 渲染失败: {video.name}")
    
    # 生成统计报告
    report = {
        "total_videos": len(cropped_videos),
        "rendered_successfully": rendered_count,
        "failed_renders": failed_count,
        "success_rate": rendered_count / len(cropped_videos) if cropped_videos else 0,
        "report_time": time.strftime('%Y-%m-%d %H:%M:%S')
    }
    
    report_path = Path("results") / f"render_report_{int(time.time())}.json"
    with open(report_path, 'w') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"📊 渲染统计:")
    print(f"   总视频数: {report['total_videos']}")
    print(f"   成功渲染: {report['rendered_successfully']}")
    print(f"   失败数量: {report['failed_renders']}")
    print(f"   成功率: {report['success_rate']:.1%}")
    print(f"📄 报告已生成: {report_path}")
    
    print(f"🎉 渲染执行完成")
    return report['success_rate'] > 0.8


def show_results():
    """显示结果"""
    print("\n📊 执行结果汇总")
    print("=" * 50)
    
    directories_to_check = [
        ("input_videos", "输入视频"),
        ("cropped_videos", "裁剪视频"), 
        ("runtime_env", "运行环境"),
        ("output", "渲染输出"),
        ("results", "统计报告")
    ]
    
    for dir_name, desc in directories_to_check:
        dir_path = Path(dir_name)
        if dir_path.exists():
            files = list(dir_path.iterdir())
            print(f"📂 {desc}: {len(files)} 个文件")
            for file_path in files[:3]:
                print(f"   📄 {file_path.name}")
            if len(files) > 3:
                print(f"   ... 还有 {len(files) - 3} 个文件")
        else:
            print(f"📂 {desc}: 目录不存在")


def cleanup_demo():
    """清理演示文件"""
    print("\n🧹 清理演示文件...")
    
    import shutil
    cleanup_dirs = [
        "input_videos", "cropped_videos", "demo_cpp", 
        "runtime_env", "output", "results", "logs"
    ]
    
    for dir_name in cleanup_dirs:
        dir_path = Path(dir_name)
        if dir_path.exists():
            try:
                shutil.rmtree(dir_path)
                print(f"✅ 已清理: {dir_name}/")
            except Exception as e:
                print(f"⚠️  清理失败 {dir_name}/: {e}")
    
    print("✅ 演示文件清理完成")


def main():
    """主函数"""
    print("🎯 DMS自动化分析与渲染平台 - 简化演示")
    print("=" * 60)
    print("这是一个功能演示，展示平台的核心工作流程")
    print("使用模拟数据，演示完整的处理流程")
    print("=" * 60)
    
    try:
        # 创建演示文件
        create_demo_files()
        
        # 执行工作流
        stages = [
            ("视频预处理", simulate_video_processing),
            ("环境设置", simulate_environment_setup),
            ("渲染执行", simulate_rendering)
        ]
        
        all_success = True
        for stage_name, stage_func in stages:
            try:
                success = stage_func()
                if not success:
                    all_success = False
                    print(f"⚠️  {stage_name} 阶段有警告")
            except Exception as e:
                print(f"❌ {stage_name} 阶段失败: {e}")
                all_success = False
        
        # 显示结果
        show_results()
        
        # 最终状态
        if all_success:
            print(f"\n🎉 演示完成！所有阶段执行成功")
        else:
            print(f"\n⚠️  演示完成，但有部分阶段存在问题")
        
        # 询问是否清理
        print("\n" + "=" * 60)
        try:
            response = input("是否清理演示文件？[Y/n]: ").strip().lower()
            if not response or response in ['y', 'yes', '是']:
                cleanup_demo()
            else:
                print("演示文件已保留，您可以手动检查生成的文件")
        except (KeyboardInterrupt, EOFError):
            print("\n演示文件已保留")
        
        print("\n🎉 感谢使用DMS自动化分析与渲染平台！")
        
    except KeyboardInterrupt:
        print("\n\n⏹️  演示被用户中断")
    except Exception as e:
        print(f"\n❌ 演示过程中发生异常: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
