#!/usr/bin/env python3
"""
DMS自动化分析与渲染平台 - 快速启动脚本

提供一键启动、配置检查、测试运行等功能
"""

import os
import sys
import json
import argparse
import subprocess
from pathlib import Path

def check_dependencies():
    """检查依赖项"""
    print("🔍 检查系统依赖...")
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("❌ Python版本过低，需要Python 3.7+")
        return False
    print(f"✅ Python版本: {sys.version}")
    
    # 检查必需的Python包
    required_packages = ['paramiko', 'rich', 'jsonschema']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}: 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package}: 未安装")
    
    if missing_packages:
        print(f"\n📦 安装缺失的包:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    # 检查FFmpeg
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print("✅ FFmpeg: 已安装")
        else:
            print("❌ FFmpeg: 安装异常")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("❌ FFmpeg: 未安装或不在PATH中")
        return False
    
    print("✅ 所有依赖检查通过")
    return True

def create_default_config():
    """创建默认配置文件"""
    print("📝 创建默认配置文件...")
    
    config_dir = Path("config")
    config_dir.mkdir(exist_ok=True)
    
    default_config = {
        "video_processing": {
            "enabled": True,
            "input_directory": "./input_videos/",
            "output_directory": "./cropped_videos/",
            "max_workers": 2,
            "force_reprocess": False,
            "duplicate_detection": {
                "enabled": True,
                "strategy": "filename_based"
            }
        },
        "environment": {
            "enabled": True,
            "cpp_source_directory": "../BYD_HKH_R_2.01.07.2025.07.08.4_x86/",
            "runtime_directory": "./runtime_env/",
            "required_files": [
                "test_dms_internal_postmortem",
                "libtx_dms.so",
                "FaceDetection.ovm",
                "FaceKeypoints.ovm",
                "eye.ovm"
            ],
            "config_files": ["ip_port.json", "calidata.json"],
            "confirmation": {
                "timeout_seconds": 10,
                "default_action": "confirm"
            }
        },
        "remote": {
            "enabled": False,  # 默认禁用，需要用户配置
            "ssh": {
                "host": "***********",
                "port": 22,
                "username": "user",
                "key_file": "~/.ssh/id_rsa",
                "timeout": 30,
                "retry_count": 3
            },
            "service": {
                "path": "/userfs/tx_dms_oax_test_tool_update",
                "port": 1180,
                "start_command": "cd /userfs && ./tx_dms_oax_test_tool_update",
                "max_wait_time": 60
            },
            "model_sync": {
                "enabled": True,
                "local_model_path": "./runtime_env/",
                "remote_model_path": "/userfs/models/",
                "model_files": ["FaceDetection.ovm", "FaceKeypoints.ovm", "eye.ovm"],
                "verify_md5": True
            }
        },
        "rendering": {
            "enabled": True,
            "max_concurrent_tasks": 2,
            "task_timeout": 3600,
            "output_directory": "./output/",
            "batch_size": 10,
            "min_success_rate": 0.8,
            "results": {
                "directory": "./results/",
                "generate_reports": True,
                "verify_outputs": True,
                "collect_statistics": True
            }
        },
        "logging": {
            "level": "INFO",
            "file": "./logs/pipeline.log",
            "max_size": "10MB",
            "backup_count": 5
        }
    }
    
    config_file = config_dir / "pipeline_config.json"
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(default_config, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 默认配置已创建: {config_file}")
    return str(config_file)

def create_directories():
    """创建必需的目录"""
    print("📁 创建项目目录...")
    
    directories = [
        "input_videos",
        "cropped_videos", 
        "runtime_env",
        "output",
        "results",
        "logs",
        "temp"
    ]
    
    for dir_name in directories:
        Path(dir_name).mkdir(exist_ok=True)
        print(f"✅ 目录已创建: {dir_name}/")

def run_tests():
    """运行测试套件"""
    print("🧪 运行测试套件...")
    
    test_files = [
        "test_video_processing.py",
        "test_environment.py", 
        "test_remote.py",
        "test_rendering.py"
    ]
    
    all_passed = True
    
    for test_file in test_files:
        if os.path.exists(test_file):
            print(f"\n🔬 运行 {test_file}...")
            try:
                result = subprocess.run([sys.executable, test_file], 
                                      capture_output=True, text=True, timeout=60)
                if result.returncode == 0:
                    print(f"✅ {test_file}: 通过")
                else:
                    print(f"❌ {test_file}: 失败")
                    print(result.stderr)
                    all_passed = False
            except subprocess.TimeoutExpired:
                print(f"⏰ {test_file}: 超时")
                all_passed = False
            except Exception as e:
                print(f"❌ {test_file}: 异常 - {e}")
                all_passed = False
        else:
            print(f"⚠️  {test_file}: 文件不存在")
    
    if all_passed:
        print("\n🎉 所有测试通过！")
    else:
        print("\n❌ 部分测试失败，请检查错误信息")
    
    return all_passed

def run_pipeline(config_file, start_from=None, debug=False):
    """运行主管道"""
    print(f"🚀 启动DMS自动化分析与渲染平台...")
    print(f"📋 配置文件: {config_file}")
    
    if start_from:
        print(f"🎯 从阶段开始: {start_from}")
    
    cmd = [sys.executable, "dms_automation_main.py", "--config", config_file]
    
    if start_from:
        cmd.extend(["--start-from", start_from])
    
    if debug:
        cmd.append("--debug")
    
    try:
        print("⏳ 执行中...")
        result = subprocess.run(cmd, text=True)
        
        if result.returncode == 0:
            print("✅ 管道执行完成")
        else:
            print("❌ 管道执行失败")
        
        return result.returncode == 0
        
    except KeyboardInterrupt:
        print("\n⏹️  用户中断执行")
        return False
    except Exception as e:
        print(f"❌ 执行异常: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="DMS自动化分析与渲染平台 - 快速启动")
    parser.add_argument("--check-deps", action="store_true", help="检查系统依赖")
    parser.add_argument("--create-config", action="store_true", help="创建默认配置文件")
    parser.add_argument("--create-dirs", action="store_true", help="创建项目目录")
    parser.add_argument("--run-tests", action="store_true", help="运行测试套件")
    parser.add_argument("--run", action="store_true", help="运行主管道")
    parser.add_argument("--config", default="config/pipeline_config.json", help="配置文件路径")
    parser.add_argument("--start-from", help="从指定阶段开始")
    parser.add_argument("--debug", action="store_true", help="调试模式")
    parser.add_argument("--setup", action="store_true", help="完整设置（检查依赖+创建配置+创建目录）")
    
    args = parser.parse_args()
    
    print("🎯 DMS自动化分析与渲染平台 - 快速启动工具")
    print("=" * 50)
    
    if args.setup:
        print("🔧 执行完整设置...")
        if not check_dependencies():
            return 1
        create_directories()
        config_file = create_default_config()
        print(f"\n✅ 设置完成！")
        print(f"📝 请编辑配置文件: {config_file}")
        print(f"🚀 然后运行: python quick_start.py --run")
        return 0
    
    if args.check_deps:
        if not check_dependencies():
            return 1
    
    if args.create_dirs:
        create_directories()
    
    if args.create_config:
        create_default_config()
    
    if args.run_tests:
        if not run_tests():
            return 1
    
    if args.run:
        if not os.path.exists(args.config):
            print(f"❌ 配置文件不存在: {args.config}")
            print("💡 运行 'python quick_start.py --create-config' 创建默认配置")
            return 1
        
        if not run_pipeline(args.config, args.start_from, args.debug):
            return 1
    
    if not any([args.check_deps, args.create_dirs, args.create_config, 
                args.run_tests, args.run, args.setup]):
        parser.print_help()
        print("\n💡 快速开始:")
        print("1. python quick_start.py --setup          # 完整设置")
        print("2. python quick_start.py --run-tests      # 运行测试")
        print("3. python quick_start.py --run            # 运行管道")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
