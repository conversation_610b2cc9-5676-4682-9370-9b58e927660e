{"verification_timestamp": "2025-07-15T10:04:39.955175", "total_verification_time": 0.22635340690612793, "summary": {"total_tests": 40, "passed_tests": 40, "failed_tests": 0, "success_rate": 100.0}, "test_results": {"Python版本": {"success": true, "message": "Python 3.8.19", "details": {}, "timestamp": 1752545079.7288406}, "FFmpeg": {"success": true, "message": "已安装: ffmpeg version 4.2.2 Copyright (c) 2000-2019 the FFmpeg developers", "details": {}, "timestamp": 1752545079.7339852}, "Python包-paramiko": {"success": true, "message": "已安装", "details": {}, "timestamp": 1752545079.7680333}, "Python包-rich": {"success": true, "message": "已安装", "details": {}, "timestamp": 1752545079.7684312}, "Python包-jsonschema": {"success": true, "message": "已安装", "details": {}, "timestamp": 1752545079.8150938}, "文件-dms_automation_main.py": {"success": true, "message": "存在", "details": {}, "timestamp": 1752545079.8151238}, "文件-quick_start.py": {"success": true, "message": "存在", "details": {}, "timestamp": 1752545079.81513}, "文件-core/task_orchestrator.py": {"success": true, "message": "存在", "details": {}, "timestamp": 1752545079.815135}, "文件-core/config_manager.py": {"success": true, "message": "存在", "details": {}, "timestamp": 1752545079.8151393}, "文件-utils/file_utils.py": {"success": true, "message": "存在", "details": {}, "timestamp": 1752545079.8151436}, "文件-utils/validation_utils.py": {"success": true, "message": "存在", "details": {}, "timestamp": 1752545079.8151479}, "文件-utils/logger_config.py": {"success": true, "message": "存在", "details": {}, "timestamp": 1752545079.8151524}, "文件-config/pipeline_config.json": {"success": true, "message": "存在", "details": {}, "timestamp": 1752545079.8151567}, "目录-core": {"success": true, "message": "存在", "details": {}, "timestamp": 1752545079.8151639}, "目录-utils": {"success": true, "message": "存在", "details": {}, "timestamp": 1752545079.8151693}, "目录-config": {"success": true, "message": "存在", "details": {}, "timestamp": 1752545079.8151743}, "目录-logs": {"success": true, "message": "存在", "details": {}, "timestamp": 1752545079.8151796}, "目录-input_videos": {"success": true, "message": "存在", "details": {}, "timestamp": 1752545079.8151848}, "目录-output": {"success": true, "message": "存在", "details": {}, "timestamp": 1752545079.8151906}, "目录-results": {"success": true, "message": "存在", "details": {}, "timestamp": 1752545079.8151965}, "JSON格式": {"success": true, "message": "配置文件JSON格式正确", "details": {}, "timestamp": 1752545079.8152945}, "配置节-video_processing": {"success": true, "message": "存在", "details": {}, "timestamp": 1752545079.8153005}, "配置节-environment": {"success": true, "message": "存在", "details": {}, "timestamp": 1752545079.8153036}, "配置节-remote": {"success": true, "message": "存在", "details": {}, "timestamp": 1752545079.8153062}, "配置节-rendering": {"success": true, "message": "存在", "details": {}, "timestamp": 1752545079.8153088}, "配置验证": {"success": true, "message": "配置验证通过", "details": {}, "timestamp": 1752545079.8165193}, "权限-dms_automation_main.py": {"success": true, "message": "可读", "details": {}, "timestamp": 1752545079.8165438}, "权限-quick_start.py": {"success": true, "message": "可读", "details": {}, "timestamp": 1752545079.8165512}, "写权限-logs": {"success": true, "message": "可写", "details": {}, "timestamp": 1752545079.8165588}, "写权限-output": {"success": true, "message": "可写", "details": {}, "timestamp": 1752545079.8165648}, "写权限-results": {"success": true, "message": "可写", "details": {}, "timestamp": 1752545079.8165696}, "写权限-temp": {"success": true, "message": "可写", "details": {}, "timestamp": 1752545079.8165753}, "配置加载": {"success": true, "message": "配置管理器工作正常", "details": {}, "timestamp": 1752545079.8167257}, "依赖检查": {"success": true, "message": "依赖检查通过", "details": {}, "timestamp": 1752545079.9351697}, "基本功能": {"success": true, "message": "Python环境正常", "details": {}, "timestamp": 1752545079.9484434}, "文件工具": {"success": true, "message": "文件操作功能正常", "details": {}, "timestamp": 1752545079.9490485}, "MD5计算": {"success": true, "message": "MD5计算功能正常", "details": {}, "timestamp": 1752545079.949107}, "验证工具": {"success": true, "message": "验证工具功能正常", "details": {}, "timestamp": 1752545079.949533}, "启动性能": {"success": true, "message": "启动时间: 0.00秒", "details": {}, "timestamp": 1752545079.9495513}, "内存使用": {"success": true, "message": "内存使用: 35.8MB", "details": {}, "timestamp": 1752545079.9551504}}, "deployment_status": "PASSED", "recommendations": []}