{"verification_timestamp": "2025-07-15T10:02:15.662323", "total_verification_time": 0.3673362731933594, "summary": {"total_tests": 40, "passed_tests": 33, "failed_tests": 7, "success_rate": 82.5}, "test_results": {"Python版本": {"success": true, "message": "Python 3.8.19", "details": {}, "timestamp": 1752544935.2950058}, "FFmpeg": {"success": true, "message": "已安装: ffmpeg version 4.2.2 Copyright (c) 2000-2019 the FFmpeg developers", "details": {}, "timestamp": 1752544935.3002307}, "Python包-paramiko": {"success": true, "message": "已安装", "details": {}, "timestamp": 1752544935.3320482}, "Python包-rich": {"success": true, "message": "已安装", "details": {}, "timestamp": 1752544935.3324442}, "Python包-jsonschema": {"success": true, "message": "已安装", "details": {}, "timestamp": 1752544935.376534}, "文件-dms_automation_main.py": {"success": true, "message": "存在", "details": {}, "timestamp": 1752544935.3765604}, "文件-quick_start.py": {"success": true, "message": "存在", "details": {}, "timestamp": 1752544935.3765671}, "文件-core/task_orchestrator.py": {"success": true, "message": "存在", "details": {}, "timestamp": 1752544935.3765717}, "文件-core/config_manager.py": {"success": false, "message": "缺失", "details": {}, "timestamp": 1752544935.3765829}, "文件-utils/file_utils.py": {"success": true, "message": "存在", "details": {}, "timestamp": 1752544935.376589}, "文件-utils/validation_utils.py": {"success": true, "message": "存在", "details": {}, "timestamp": 1752544935.376593}, "文件-utils/logger_config.py": {"success": true, "message": "存在", "details": {}, "timestamp": 1752544935.3765981}, "文件-config/pipeline_config.json": {"success": true, "message": "存在", "details": {}, "timestamp": 1752544935.3766026}, "目录-core": {"success": true, "message": "存在", "details": {}, "timestamp": 1752544935.3766098}, "目录-utils": {"success": true, "message": "存在", "details": {}, "timestamp": 1752544935.3766153}, "目录-config": {"success": true, "message": "存在", "details": {}, "timestamp": 1752544935.3766198}, "目录-logs": {"success": false, "message": "缺失", "details": {}, "timestamp": 1752544935.376624}, "目录-input_videos": {"success": false, "message": "缺失", "details": {}, "timestamp": 1752544935.376628}, "目录-output": {"success": false, "message": "缺失", "details": {}, "timestamp": 1752544935.3766317}, "目录-results": {"success": true, "message": "存在", "details": {}, "timestamp": 1752544935.3766375}, "JSON格式": {"success": true, "message": "配置文件JSON格式正确", "details": {}, "timestamp": 1752544935.376696}, "配置节-video_processing": {"success": true, "message": "存在", "details": {}, "timestamp": 1752544935.376701}, "配置节-environment": {"success": true, "message": "存在", "details": {}, "timestamp": 1752544935.3767042}, "配置节-remote": {"success": true, "message": "存在", "details": {}, "timestamp": 1752544935.3767068}, "配置节-rendering": {"success": true, "message": "存在", "details": {}, "timestamp": 1752544935.3767092}, "配置验证": {"success": false, "message": "配置验证异常: attempted relative import beyond top-level package", "details": {}, "timestamp": 1752544935.397808}, "权限-dms_automation_main.py": {"success": true, "message": "可读", "details": {}, "timestamp": 1752544935.397857}, "权限-quick_start.py": {"success": true, "message": "可读", "details": {}, "timestamp": 1752544935.3978643}, "写权限-logs": {"success": true, "message": "目录已创建", "details": {}, "timestamp": 1752544935.3979213}, "写权限-output": {"success": true, "message": "目录已创建", "details": {}, "timestamp": 1752544935.397944}, "写权限-results": {"success": true, "message": "可写", "details": {}, "timestamp": 1752544935.397952}, "写权限-temp": {"success": true, "message": "可写", "details": {}, "timestamp": 1752544935.3979576}, "配置加载": {"success": false, "message": "配置加载失败: Traceback (most recent call last):\n  File \"<string>\", line 1, in <module>\n  File \"/home/<USER>/tool_kit/dms_postmortem_end2end/dms_automation_pipeline/core/__init__.py\", line 12, in <module>\n    from .task_orchestrator import TaskOrchestrator\n  File \"/home/<USER>/tool_kit/dms_postmortem_end2end/dms_automation_pipeline/core/task_orchestrator.py\", line 27, in <module>\n    from ..utils.logger_config import setup_module_logger\nValueError: attempted relative import beyond top-level package\n", "details": {}, "timestamp": 1752544935.4579847}, "依赖检查": {"success": true, "message": "依赖检查通过", "details": {}, "timestamp": 1752544935.577223}, "基本功能": {"success": true, "message": "Python环境正常", "details": {}, "timestamp": 1752544935.59001}, "文件工具": {"success": true, "message": "文件操作功能正常", "details": {}, "timestamp": 1752544935.593598}, "MD5计算": {"success": true, "message": "MD5计算功能正常", "details": {}, "timestamp": 1752544935.5936415}, "验证工具": {"success": true, "message": "验证工具功能正常", "details": {}, "timestamp": 1752544935.5938234}, "启动性能": {"success": false, "message": "启动时间过长: 0.06秒", "details": {}, "timestamp": 1752544935.6542304}, "内存使用": {"success": true, "message": "内存使用: 40.9MB", "details": {}, "timestamp": 1752544935.662281}}, "deployment_status": "FAILED", "recommendations": ["检查配置文件，运行 python quick_start.py --create-config", "检查文件结构，运行 python quick_start.py --setup"]}