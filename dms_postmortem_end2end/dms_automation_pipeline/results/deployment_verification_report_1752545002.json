{"verification_timestamp": "2025-07-15T10:03:22.205075", "total_verification_time": 0.3741917610168457, "summary": {"total_tests": 40, "passed_tests": 37, "failed_tests": 3, "success_rate": 92.5}, "test_results": {"Python版本": {"success": true, "message": "Python 3.8.19", "details": {}, "timestamp": 1752545001.8309}, "FFmpeg": {"success": true, "message": "已安装: ffmpeg version 4.2.2 Copyright (c) 2000-2019 the FFmpeg developers", "details": {}, "timestamp": 1752545001.8364289}, "Python包-paramiko": {"success": true, "message": "已安装", "details": {}, "timestamp": 1752545001.8703995}, "Python包-rich": {"success": true, "message": "已安装", "details": {}, "timestamp": 1752545001.8708253}, "Python包-jsonschema": {"success": true, "message": "已安装", "details": {}, "timestamp": 1752545001.9178343}, "文件-dms_automation_main.py": {"success": true, "message": "存在", "details": {}, "timestamp": 1752545001.9178624}, "文件-quick_start.py": {"success": true, "message": "存在", "details": {}, "timestamp": 1752545001.9178684}, "文件-core/task_orchestrator.py": {"success": true, "message": "存在", "details": {}, "timestamp": 1752545001.917873}, "文件-core/config_manager.py": {"success": true, "message": "存在", "details": {}, "timestamp": 1752545001.9178767}, "文件-utils/file_utils.py": {"success": true, "message": "存在", "details": {}, "timestamp": 1752545001.9178813}, "文件-utils/validation_utils.py": {"success": true, "message": "存在", "details": {}, "timestamp": 1752545001.9178858}, "文件-utils/logger_config.py": {"success": true, "message": "存在", "details": {}, "timestamp": 1752545001.91789}, "文件-config/pipeline_config.json": {"success": true, "message": "存在", "details": {}, "timestamp": 1752545001.9178948}, "目录-core": {"success": true, "message": "存在", "details": {}, "timestamp": 1752545001.9179027}, "目录-utils": {"success": true, "message": "存在", "details": {}, "timestamp": 1752545001.917908}, "目录-config": {"success": true, "message": "存在", "details": {}, "timestamp": 1752545001.9179127}, "目录-logs": {"success": true, "message": "存在", "details": {}, "timestamp": 1752545001.917918}, "目录-input_videos": {"success": true, "message": "存在", "details": {}, "timestamp": 1752545001.9179237}, "目录-output": {"success": true, "message": "存在", "details": {}, "timestamp": 1752545001.917929}, "目录-results": {"success": true, "message": "存在", "details": {}, "timestamp": 1752545001.9179344}, "JSON格式": {"success": true, "message": "配置文件JSON格式正确", "details": {}, "timestamp": 1752545001.917996}, "配置节-video_processing": {"success": true, "message": "存在", "details": {}, "timestamp": 1752545001.9180014}, "配置节-environment": {"success": true, "message": "存在", "details": {}, "timestamp": 1752545001.9180048}, "配置节-remote": {"success": true, "message": "存在", "details": {}, "timestamp": 1752545001.9180074}, "配置节-rendering": {"success": true, "message": "存在", "details": {}, "timestamp": 1752545001.9180095}, "配置验证": {"success": false, "message": "配置验证异常: attempted relative import beyond top-level package", "details": {}, "timestamp": 1752545001.9395344}, "权限-dms_automation_main.py": {"success": true, "message": "可读", "details": {}, "timestamp": 1752545001.9395878}, "权限-quick_start.py": {"success": true, "message": "可读", "details": {}, "timestamp": 1752545001.9395955}, "写权限-logs": {"success": true, "message": "可写", "details": {}, "timestamp": 1752545001.9396029}, "写权限-output": {"success": true, "message": "可写", "details": {}, "timestamp": 1752545001.9396088}, "写权限-results": {"success": true, "message": "可写", "details": {}, "timestamp": 1752545001.9396143}, "写权限-temp": {"success": true, "message": "可写", "details": {}, "timestamp": 1752545001.9396193}, "配置加载": {"success": false, "message": "配置加载失败: Traceback (most recent call last):\n  File \"<string>\", line 1, in <module>\n  File \"/home/<USER>/tool_kit/dms_postmortem_end2end/dms_automation_pipeline/core/__init__.py\", line 12, in <module>\n    from .task_orchestrator import TaskOrchestrator\n  File \"/home/<USER>/tool_kit/dms_postmortem_end2end/dms_automation_pipeline/core/task_orchestrator.py\", line 27, in <module>\n    from ..utils.logger_config import setup_module_logger\nValueError: attempted relative import beyond top-level package\n", "details": {}, "timestamp": 1752545001.9993558}, "依赖检查": {"success": true, "message": "依赖检查通过", "details": {}, "timestamp": 1752545002.1194017}, "基本功能": {"success": true, "message": "Python环境正常", "details": {}, "timestamp": 1752545002.1323354}, "文件工具": {"success": true, "message": "文件操作功能正常", "details": {}, "timestamp": 1752545002.1360495}, "MD5计算": {"success": true, "message": "MD5计算功能正常", "details": {}, "timestamp": 1752545002.136097}, "验证工具": {"success": true, "message": "验证工具功能正常", "details": {}, "timestamp": 1752545002.136273}, "启动性能": {"success": false, "message": "启动时间过长: 0.06秒", "details": {}, "timestamp": 1752545002.1990864}, "内存使用": {"success": true, "message": "内存使用: 40.6MB", "details": {}, "timestamp": 1752545002.2050452}}, "deployment_status": "FAILED", "recommendations": ["检查配置文件，运行 python quick_start.py --create-config"]}