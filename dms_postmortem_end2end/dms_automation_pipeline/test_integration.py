#!/usr/bin/env python3
"""
集成测试脚本

验证VideoPreprocessor与TaskOrchestrator的集成
"""

import sys
import os
import tempfile
import json
from unittest.mock import patch, MagicMock

# 添加父目录到Python路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

from dms_automation_pipeline.core.task_orchestrator import TaskOrchestrator, StageResult
from dms_automation_pipeline.core.video_processing_executor import VideoProcessingExecutor
from dms_automation_pipeline.core.video_preprocessor import VideoProcessingResult


def create_test_config_with_videos(temp_dir: str) -> dict:
    """创建包含视频文件的测试配置"""
    input_dir = os.path.join(temp_dir, "input_videos")
    output_dir = os.path.join(temp_dir, "output_videos")
    
    # 创建目录
    os.makedirs(input_dir, exist_ok=True)
    os.makedirs(output_dir, exist_ok=True)
    
    # 创建测试视频文件
    test_videos = ["test1.mkv", "test2.mp4"]
    for video in test_videos:
        video_path = os.path.join(input_dir, video)
        with open(video_path, 'w') as f:
            f.write("fake video content")
    
    return {
        "project_info": {
            "name": "集成测试项目",
            "version": "1.0.0"
        },
        "video_processing": {
            "enabled": True,
            "input_directory": input_dir,
            "output_directory": output_dir,
            "supported_formats": [".mkv", ".mp4", ".avi"],
            "time_ranges": [
                {
                    "start": "00:04:57",
                    "end": "00:05:17",
                    "description": "测试片段"
                }
            ],
            "roi_default": "1920:1080:0:0",
            "deduplication": {
                "enabled": True,
                "force_reprocess": False,
                "naming_pattern": "{base_name}_{time_range}_roi_{roi}.mp4"
            },
            "batch_processing": {
                "max_parallel": 2,
                "retry_count": 1,
                "timeout_seconds": 30
            }
        },
        "environment": {
            "enabled": True,
            "cpp_source_directory": "./cpp/",
            "runtime_directory": "./runtime/"
        },
        "remote": {
            "enabled": True,
            "ssh": {
                "host": "***********",
                "username": "test"
            },
            "service": {
                "path": "/test/path",
                "port": 1180
            }
        },
        "rendering": {
            "enabled": True,
            "cpp_program": "./test_program",
            "output_directory": output_dir
        },
        "logging": {
            "level": "INFO",
            "console_output": True,
            "file_output": False
        },
        "pipeline": {
            "auto_resume": True,
            "save_state": True,
            "progress_display": True
        }
    }


def test_video_processing_executor():
    """测试视频处理执行器"""
    print("=== 测试视频处理执行器 ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config = create_test_config_with_videos(temp_dir)
        executor = VideoProcessingExecutor(config)
        
        # 测试配置验证
        valid, error_msg = executor.validate_video_processing_config()
        print(f"✓ 配置验证结果: {valid}")
        if not valid:
            print(f"  错误信息: {error_msg}")
        assert valid == True
        
        # 测试批量配置准备
        batch_config = executor.prepare_batch_config_from_config()
        print(f"✓ 批量配置数量: {len(batch_config)}")
        assert len(batch_config) == 2  # 两个测试视频
        
        # 验证批量配置格式
        for config_item in batch_config:
            assert "path" in config_item
            assert "time_ranges" in config_item
            assert "roi" in config_item
            assert len(config_item["time_ranges"]) == 1
        
        print("✓ 视频处理执行器测试通过")
    
    return True


def test_orchestrator_integration():
    """测试TaskOrchestrator集成"""
    print("\n=== 测试TaskOrchestrator集成 ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config = create_test_config_with_videos(temp_dir)
        orchestrator = TaskOrchestrator(config)
        
        # 检查是否自动注册了视频处理执行器
        assert "VIDEO_PROCESSING" in orchestrator.stage_executors
        print("✓ 视频处理执行器已自动注册")
        
        # Mock视频处理以避免实际FFmpeg调用
        with patch.object(orchestrator.stage_executors["VIDEO_PROCESSING"].__self__.video_processor, 'process_batch') as mock_process:
            # 模拟成功的处理结果
            mock_result = VideoProcessingResult(
                success=True,
                message="批量处理成功: 2个视频",
                output_files=["output1.mp4", "output2.mp4"]
            )
            mock_result.processing_time = 1.5
            mock_result.skipped_files = []
            mock_process.return_value = mock_result
            
            # 执行视频处理阶段
            result = orchestrator.execute_stage("VIDEO_PROCESSING")
            
            print(f"✓ 阶段执行结果: {result.success}")
            print(f"✓ 执行消息: {result.message}")
            print(f"✓ 输出文件数: {len(result.data.get('output_files', []))}")
            print(f"✓ 处理时间: {result.data.get('processing_time', 0):.2f}秒")
            
            assert result.success == True
            assert len(result.data.get('output_files', [])) == 2
            assert result.data.get('processing_time', 0) > 0
            
            # 验证状态转换
            assert orchestrator.current_state.value == "ENV_SETUP"
            print("✓ 状态正确转换到ENV_SETUP")
        
        print("✓ TaskOrchestrator集成测试通过")
    
    return True


def test_orchestrator_with_failures():
    """测试TaskOrchestrator处理失败情况"""
    print("\n=== 测试TaskOrchestrator处理失败情况 ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config = create_test_config_with_videos(temp_dir)
        orchestrator = TaskOrchestrator(config)
        
        # Mock视频处理失败
        with patch.object(orchestrator.stage_executors["VIDEO_PROCESSING"].__self__.video_processor, 'process_batch') as mock_process:
            # 模拟失败的处理结果
            mock_result = VideoProcessingResult(
                success=False,
                message="批量处理失败",
                error="模拟的处理错误"
            )
            mock_result.processing_time = 0.5
            mock_process.return_value = mock_result
            
            # 执行视频处理阶段
            result = orchestrator.execute_stage("VIDEO_PROCESSING")
            
            print(f"✓ 失败处理结果: {result.success}")
            print(f"✓ 错误信息: {result.error}")
            
            assert result.success == False
            assert result.error is not None
            
            # 验证状态转换到失败状态
            assert orchestrator.current_state.value == "FAILED"
            print("✓ 状态正确转换到FAILED")
        
        print("✓ 失败情况处理测试通过")
    
    return True


def test_state_persistence():
    """测试状态持久化"""
    print("\n=== 测试状态持久化 ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config = create_test_config_with_videos(temp_dir)
        
        # 第一个orchestrator执行视频处理
        orchestrator1 = TaskOrchestrator(config)
        
        with patch.object(orchestrator1.stage_executors["VIDEO_PROCESSING"].__self__.video_processor, 'process_batch') as mock_process:
            mock_result = VideoProcessingResult(
                success=True,
                message="批量处理成功",
                output_files=["output1.mp4", "output2.mp4"]
            )
            mock_result.processing_time = 1.0
            mock_process.return_value = mock_result
            
            result1 = orchestrator1.execute_stage("VIDEO_PROCESSING")
            assert result1.success == True
            assert orchestrator1.current_state.value == "ENV_SETUP"
        
        # 第二个orchestrator从状态文件恢复
        orchestrator2 = TaskOrchestrator.load_from_state(config)
        
        print(f"✓ 恢复的状态: {orchestrator2.current_state.value}")
        print(f"✓ 阶段结果数量: {len(orchestrator2.stage_results)}")
        
        assert orchestrator2.current_state.value == "ENV_SETUP"
        assert "VIDEO_PROCESSING" in orchestrator2.stage_results
        assert orchestrator2.stage_results["VIDEO_PROCESSING"].success == True
        
        print("✓ 状态持久化测试通过")
    
    return True


def test_processing_summary():
    """测试处理摘要信息"""
    print("\n=== 测试处理摘要信息 ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config = create_test_config_with_videos(temp_dir)
        executor = VideoProcessingExecutor(config)
        
        summary = executor.get_processing_summary()
        
        print(f"✓ 输入目录: {summary['input_directory']}")
        print(f"✓ 输出目录: {summary['output_directory']}")
        print(f"✓ 支持格式: {summary['supported_formats']}")
        print(f"✓ 去重启用: {summary['deduplication_enabled']}")
        print(f"✓ 最大并行数: {summary['max_parallel']}")
        
        assert summary['input_directory'] is not None
        assert summary['output_directory'] is not None
        assert len(summary['supported_formats']) > 0
        assert isinstance(summary['deduplication_enabled'], bool)
        assert summary['max_parallel'] > 0
        
        print("✓ 处理摘要信息测试通过")
    
    return True


def main():
    """主测试函数"""
    try:
        print("开始集成测试...")
        
        # 运行所有测试
        tests = [
            test_video_processing_executor,
            test_orchestrator_integration,
            test_orchestrator_with_failures,
            test_state_persistence,
            test_processing_summary
        ]
        
        for test in tests:
            if not test():
                print(f"❌ 测试失败: {test.__name__}")
                return False
                
        print("\n🎉 所有集成测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
