#!/usr/bin/env python3
"""
基础工具类测试脚本

用于验证file_utils、validation_utils、logger_config的功能
"""

import sys
import os
import tempfile
import hashlib

# 添加父目录到Python路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

from dms_automation_pipeline.utils.file_utils import (
    copy_with_permissions, calculate_md5, set_executable_permission,
    ensure_directory_exists, get_file_size, is_file_exists_and_readable,
    is_file_executable
)

from dms_automation_pipeline.utils.validation_utils import (
    validate_time_format, validate_path, validate_ip_address,
    validate_port, validate_roi_format, validate_time_range,
    validate_config_structure, sanitize_filename
)

from dms_automation_pipeline.utils.logger_config import (
    setup_logger, get_default_log_file, setup_module_logger
)


def test_file_utils():
    """测试文件工具函数"""
    print("=== 测试文件工具函数 ===")
    
    # 创建临时文件进行测试
    with tempfile.NamedTemporaryFile(mode='w', delete=False) as f:
        f.write("测试内容")
        test_file = f.name
    
    try:
        # 测试文件存在性检查
        assert is_file_exists_and_readable(test_file) == True
        print("✓ 文件存在性检查通过")
        
        # 测试文件大小获取
        size = get_file_size(test_file)
        assert size > 0
        print(f"✓ 文件大小获取成功: {size} 字节")
        
        # 测试MD5计算
        md5_hash = calculate_md5(test_file)
        assert md5_hash is not None
        assert len(md5_hash) == 32
        print(f"✓ MD5计算成功: {md5_hash[:8]}...")
        
        # 测试文件拷贝
        with tempfile.NamedTemporaryFile(delete=False) as f:
            dest_file = f.name
        
        result = copy_with_permissions(test_file, dest_file)
        assert result == True
        assert is_file_exists_and_readable(dest_file) == True
        print("✓ 文件拷贝成功")
        
        # 测试设置可执行权限
        result = set_executable_permission(dest_file)
        assert result == True
        print("✓ 设置可执行权限成功")
        
        # 清理
        os.remove(dest_file)
        
    finally:
        os.remove(test_file)
    
    # 测试目录创建
    with tempfile.TemporaryDirectory() as temp_dir:
        test_dir = os.path.join(temp_dir, "test_subdir", "nested")
        result = ensure_directory_exists(test_dir)
        assert result == True
        assert os.path.exists(test_dir)
        print("✓ 目录创建成功")
    
    return True


def test_validation_utils():
    """测试验证工具函数"""
    print("\n=== 测试验证工具函数 ===")
    
    # 测试时间格式验证
    assert validate_time_format("12:34:56") == True
    assert validate_time_format("00:00:00") == True
    assert validate_time_format("23:59:59") == True
    assert validate_time_format("24:00:00") == False
    assert validate_time_format("12:60:00") == False
    assert validate_time_format("invalid") == False
    print("✓ 时间格式验证通过")
    
    # 测试路径验证
    assert validate_path("/tmp", must_exist=False) == True
    assert validate_path("", must_exist=False) == False
    print("✓ 路径验证通过")
    
    # 测试IP地址验证
    assert validate_ip_address("***********") == True
    assert validate_ip_address("0.0.0.0") == True
    assert validate_ip_address("***************") == True
    assert validate_ip_address("256.1.1.1") == False
    assert validate_ip_address("192.168.1") == False
    assert validate_ip_address("invalid") == False
    print("✓ IP地址验证通过")
    
    # 测试端口验证
    assert validate_port(80) == True
    assert validate_port("8080") == True
    assert validate_port(65535) == True
    assert validate_port(0) == False
    assert validate_port(65536) == False
    assert validate_port("invalid") == False
    print("✓ 端口验证通过")
    
    # 测试ROI格式验证
    assert validate_roi_format("1920:1080:0:0") == True
    assert validate_roi_format("640:480:100:50") == True
    assert validate_roi_format("invalid") == False
    assert validate_roi_format("1920:1080:0") == False
    print("✓ ROI格式验证通过")
    
    # 测试时间范围验证
    assert validate_time_range("12:00:00", "12:30:00") == True
    assert validate_time_range("12:30:00", "12:00:00") == False
    assert validate_time_range("invalid", "12:00:00") == False
    print("✓ 时间范围验证通过")
    
    # 测试配置结构验证
    config = {"key1": "value1", "key2": "value2"}
    assert validate_config_structure(config, ["key1", "key2"]) == True
    assert validate_config_structure(config, ["key1", "key3"]) == False
    assert validate_config_structure("invalid", ["key1"]) == False
    print("✓ 配置结构验证通过")
    
    # 测试文件名清理
    assert sanitize_filename("normal_file.txt") == "normal_file.txt"
    assert sanitize_filename("file<>:name.txt") == "file___name.txt"
    assert sanitize_filename("") == "unnamed_file"
    print("✓ 文件名清理通过")
    
    return True


def test_logger_config():
    """测试日志配置"""
    print("\n=== 测试日志配置功能 ===")
    
    # 测试基本日志设置
    logger = setup_logger("test_logger", log_level="INFO", console_output=True)
    assert logger is not None
    print("✓ 基本日志设置成功")
    
    # 测试模块日志设置
    module_logger = setup_module_logger("test_module")
    assert module_logger is not None
    print("✓ 模块日志设置成功")
    
    # 测试默认日志文件路径
    log_file = get_default_log_file("test_app")
    assert log_file.endswith(".log")
    assert "test_app" in log_file
    print(f"✓ 默认日志文件路径: {log_file}")
    
    # 测试日志记录
    logger.info("这是一条测试日志")
    module_logger.debug("这是一条模块调试日志")
    print("✓ 日志记录功能正常")
    
    return True


def test_integration():
    """测试集成功能"""
    print("\n=== 测试集成功能 ===")
    
    # 创建一个综合测试场景
    with tempfile.TemporaryDirectory() as temp_dir:
        # 创建测试文件
        test_file = os.path.join(temp_dir, "test_file.txt")
        with open(test_file, 'w') as f:
            f.write("集成测试内容")
        
        # 验证文件
        assert is_file_exists_and_readable(test_file) == True
        
        # 计算MD5
        md5_hash = calculate_md5(test_file)
        assert md5_hash is not None
        
        # 验证文件名
        clean_name = sanitize_filename("test<>file.txt")
        assert clean_name == "test__file.txt"
        
        # 设置日志
        logger = setup_logger("integration_test")
        logger.info(f"集成测试完成，文件MD5: {md5_hash}")
        
        print("✓ 集成测试通过")
    
    return True


def main():
    """主测试函数"""
    try:
        print("开始测试基础工具类...")
        
        # 运行所有测试
        tests = [
            test_file_utils,
            test_validation_utils,
            test_logger_config,
            test_integration
        ]
        
        for test in tests:
            if not test():
                print(f"❌ 测试失败: {test.__name__}")
                return False
                
        print("\n🎉 所有基础工具类测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
