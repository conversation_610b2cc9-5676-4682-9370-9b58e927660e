#!/usr/bin/env python3
"""
DMS自动化分析与渲染平台 - 综合单元测试

为所有核心组件提供完整的单元测试，确保代码覆盖率>90%
"""

import sys
import os
import tempfile
import unittest
from unittest.mock import patch, MagicMock, mock_open
import json
import time
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from core.task_orchestrator import TaskOrchestrator, PipelineState
    from core.config_manager import ConfigManager
    from utils.file_utils import (
        copy_with_permissions, calculate_md5, is_file_exists_and_readable,
        ensure_directory_exists
    )
    from utils.validation_utils import (
        validate_time_format, validate_ip_address, validate_port, validate_path
    )
except ImportError as e:
    print(f"导入错误: {e}")
    print("尝试直接导入模块...")

    # 直接导入核心模块
    import importlib.util

    def load_module(module_name, file_path):
        spec = importlib.util.spec_from_file_location(module_name, file_path)
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        return module

    # 加载必需的模块
    current_dir = os.path.dirname(os.path.abspath(__file__))

    task_orchestrator_module = load_module("task_orchestrator",
                                         os.path.join(current_dir, "core", "task_orchestrator.py"))
    TaskOrchestrator = task_orchestrator_module.TaskOrchestrator
    PipelineState = task_orchestrator_module.PipelineState

    config_manager_module = load_module("config_manager",
                                      os.path.join(current_dir, "core", "config_manager.py"))
    ConfigManager = config_manager_module.ConfigManager

    file_utils_module = load_module("file_utils",
                                  os.path.join(current_dir, "utils", "file_utils.py"))
    copy_with_permissions = file_utils_module.copy_with_permissions
    calculate_md5 = file_utils_module.calculate_md5
    is_file_exists_and_readable = file_utils_module.is_file_exists_and_readable
    ensure_directory_exists = file_utils_module.ensure_directory_exists

    validation_utils_module = load_module("validation_utils",
                                        os.path.join(current_dir, "utils", "validation_utils.py"))
    validate_time_format = validation_utils_module.validate_time_format
    validate_ip_address = validation_utils_module.validate_ip_address
    validate_port = validation_utils_module.validate_port
    validate_path = validation_utils_module.validate_path


class TestTaskOrchestrator(unittest.TestCase):
    """TaskOrchestrator单元测试"""
    
    def setUp(self):
        """测试前准备"""
        self.test_config = {
            "video_processing": {"enabled": True},
            "environment": {"enabled": True},
            "remote": {"enabled": False},
            "rendering": {"enabled": True}
        }
        
    def test_initialization(self):
        """测试初始化"""
        orchestrator = TaskOrchestrator(self.test_config)
        self.assertEqual(orchestrator.current_state, PipelineState.INITIAL)
        self.assertIsNotNone(orchestrator.config)
        
    def test_state_transitions(self):
        """测试状态转换"""
        orchestrator = TaskOrchestrator(self.test_config)
        
        # 测试正常状态转换
        orchestrator.set_state(PipelineState.VIDEO_PROCESSING)
        self.assertEqual(orchestrator.current_state, PipelineState.VIDEO_PROCESSING)
        
        orchestrator.set_state(PipelineState.ENV_SETUP)
        self.assertEqual(orchestrator.current_state, PipelineState.ENV_SETUP)
        
    def test_state_persistence(self):
        """测试状态持久化"""
        with tempfile.TemporaryDirectory() as temp_dir:
            state_file = os.path.join(temp_dir, ".pipeline_state.json")
            
            # 创建orchestrator并设置状态
            orchestrator = TaskOrchestrator(self.test_config)
            orchestrator.state_file = state_file
            orchestrator.set_state(PipelineState.RENDERING)
            orchestrator.save_state()
            
            # 验证状态文件存在
            self.assertTrue(os.path.exists(state_file))
            
            # 加载状态并验证
            new_orchestrator = TaskOrchestrator(self.test_config)
            new_orchestrator.state_file = state_file
            new_orchestrator.load_state()
            self.assertEqual(new_orchestrator.current_state, PipelineState.RENDERING)
            
    def test_executor_registration(self):
        """测试执行器注册"""
        orchestrator = TaskOrchestrator(self.test_config)
        
        # 检查执行器是否注册
        expected_executors = ["ENV_SETUP", "REMOTE_VALIDATION", "RENDERING"]
        for executor_name in expected_executors:
            self.assertIn(executor_name, orchestrator.stage_executors)
            
    def test_stage_execution_success(self):
        """测试阶段执行成功"""
        orchestrator = TaskOrchestrator(self.test_config)
        
        # Mock执行器
        mock_executor = MagicMock()
        mock_executor.return_value.success = True
        mock_executor.return_value.message = "Test success"
        orchestrator.stage_executors["TEST_STAGE"] = mock_executor
        
        # 执行阶段
        result = orchestrator.execute_stage("TEST_STAGE")
        self.assertTrue(result.success)
        self.assertEqual(result.message, "Test success")
        
    def test_stage_execution_failure(self):
        """测试阶段执行失败"""
        orchestrator = TaskOrchestrator(self.test_config)
        
        # Mock失败的执行器
        mock_executor = MagicMock()
        mock_executor.return_value.success = False
        mock_executor.return_value.error = "Test error"
        orchestrator.stage_executors["TEST_STAGE"] = mock_executor
        
        # 执行阶段
        result = orchestrator.execute_stage("TEST_STAGE")
        self.assertFalse(result.success)
        self.assertEqual(result.error, "Test error")


class TestConfigManager(unittest.TestCase):
    """ConfigManager单元测试"""
    
    def test_config_loading(self):
        """测试配置加载"""
        test_config = {
            "video_processing": {"enabled": True},
            "environment": {"enabled": True}
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(test_config, f)
            config_file = f.name
        
        try:
            config_manager = ConfigManager()
            loaded_config = config_manager.load_config(config_file)
            self.assertEqual(loaded_config["video_processing"]["enabled"], True)
        finally:
            os.unlink(config_file)
            
    def test_config_validation(self):
        """测试配置验证"""
        config_manager = ConfigManager()
        
        # 有效配置
        valid_config = {
            "video_processing": {"enabled": True},
            "environment": {"enabled": True},
            "remote": {"enabled": False},
            "rendering": {"enabled": True}
        }
        
        is_valid, error = config_manager.validate_config(valid_config)
        self.assertTrue(is_valid)
        self.assertEqual(error, "")
        
        # 无效配置
        invalid_config = {"invalid_key": "invalid_value"}
        is_valid, error = config_manager.validate_config(invalid_config)
        self.assertFalse(is_valid)
        self.assertNotEqual(error, "")
        
    def test_config_file_not_found(self):
        """测试配置文件不存在"""
        config_manager = ConfigManager()
        
        with self.assertRaises(FileNotFoundError):
            config_manager.load_config("nonexistent_config.json")


class TestFileUtils(unittest.TestCase):
    """FileUtils单元测试"""
    
    def test_copy_with_permissions(self):
        """测试文件拷贝和权限设置"""
        with tempfile.TemporaryDirectory() as temp_dir:
            source_file = os.path.join(temp_dir, "source.txt")
            dest_file = os.path.join(temp_dir, "dest.txt")
            
            # 创建源文件
            with open(source_file, 'w') as f:
                f.write("test content")
            os.chmod(source_file, 0o755)
            
            # 测试拷贝
            success = copy_with_permissions(source_file, dest_file)
            self.assertTrue(success)
            self.assertTrue(os.path.exists(dest_file))
            
            # 验证权限
            source_stat = os.stat(source_file)
            dest_stat = os.stat(dest_file)
            self.assertEqual(source_stat.st_mode, dest_stat.st_mode)
            
    def test_calculate_md5(self):
        """测试MD5计算"""
        with tempfile.NamedTemporaryFile(mode='w', delete=False) as f:
            f.write("test content for md5")
            test_file = f.name
        
        try:
            md5_hash = calculate_md5(test_file)
            self.assertIsNotNone(md5_hash)
            self.assertEqual(len(md5_hash), 32)  # MD5哈希长度
            
            # 相同内容应该产生相同的MD5
            md5_hash2 = calculate_md5(test_file)
            self.assertEqual(md5_hash, md5_hash2)
        finally:
            os.unlink(test_file)
            
    def test_is_file_exists_and_readable(self):
        """测试文件存在性和可读性检查"""
        with tempfile.NamedTemporaryFile(delete=False) as f:
            test_file = f.name
            f.write(b"test content")
        
        try:
            # 文件存在且可读
            self.assertTrue(is_file_exists_and_readable(test_file))
            
            # 文件不存在
            self.assertFalse(is_file_exists_and_readable("nonexistent_file.txt"))
        finally:
            os.unlink(test_file)
            
    def test_ensure_directory_exists(self):
        """测试目录创建"""
        with tempfile.TemporaryDirectory() as temp_dir:
            test_dir = os.path.join(temp_dir, "test_subdir", "nested")
            
            # 目录不存在
            self.assertFalse(os.path.exists(test_dir))
            
            # 创建目录
            success = ensure_directory_exists(test_dir)
            self.assertTrue(success)
            self.assertTrue(os.path.exists(test_dir))
            self.assertTrue(os.path.isdir(test_dir))
            
            # 目录已存在
            success2 = ensure_directory_exists(test_dir)
            self.assertTrue(success2)


class TestValidationUtils(unittest.TestCase):
    """ValidationUtils单元测试"""
    
    def test_validate_time_format(self):
        """测试时间格式验证"""
        # 有效时间格式
        valid_times = ["00:00:00", "12:34:56", "23:59:59"]
        for time_str in valid_times:
            self.assertTrue(validate_time_format(time_str))
            
        # 无效时间格式
        invalid_times = ["24:00:00", "12:60:00", "12:34:60", "invalid", "12:34"]
        for time_str in invalid_times:
            self.assertFalse(validate_time_format(time_str))
            
    def test_validate_ip_address(self):
        """测试IP地址验证"""
        # 有效IP地址
        valid_ips = ["***********", "127.0.0.1", "********", "**********"]
        for ip in valid_ips:
            self.assertTrue(validate_ip_address(ip))
            
        # 无效IP地址
        invalid_ips = ["256.1.1.1", "192.168.1", "invalid", "***********.1"]
        for ip in invalid_ips:
            self.assertFalse(validate_ip_address(ip))
            
    def test_validate_port(self):
        """测试端口验证"""
        # 有效端口
        valid_ports = [80, 443, 1180, 8080, 65535]
        for port in valid_ports:
            self.assertTrue(validate_port(port))
            
        # 无效端口
        invalid_ports = [0, -1, 65536, 100000]
        for port in invalid_ports:
            self.assertFalse(validate_port(port))
            
    def test_validate_path(self):
        """测试路径验证"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 有效路径
            self.assertTrue(validate_path(temp_dir))
            
            # 无效路径
            self.assertFalse(validate_path("/nonexistent/path"))
            self.assertFalse(validate_path(""))


class TestEdgeCases(unittest.TestCase):
    """边界条件和异常测试"""
    
    def test_empty_config(self):
        """测试空配置"""
        orchestrator = TaskOrchestrator({})
        self.assertEqual(orchestrator.current_state, PipelineState.INITIAL)
        
    def test_none_config(self):
        """测试None配置"""
        with self.assertRaises(TypeError):
            TaskOrchestrator(None)
            
    def test_large_file_md5(self):
        """测试大文件MD5计算"""
        with tempfile.NamedTemporaryFile(delete=False) as f:
            # 创建1MB的测试文件
            f.write(b"x" * (1024 * 1024))
            large_file = f.name
        
        try:
            start_time = time.time()
            md5_hash = calculate_md5(large_file)
            end_time = time.time()
            
            self.assertIsNotNone(md5_hash)
            self.assertEqual(len(md5_hash), 32)
            # 确保计算时间合理（不超过5秒）
            self.assertLess(end_time - start_time, 5.0)
        finally:
            os.unlink(large_file)
            
    def test_permission_denied(self):
        """测试权限拒绝情况"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建只读目录
            readonly_dir = os.path.join(temp_dir, "readonly")
            os.makedirs(readonly_dir)
            os.chmod(readonly_dir, 0o444)
            
            try:
                # 尝试在只读目录中创建文件应该失败
                test_file = os.path.join(readonly_dir, "test.txt")
                success = copy_with_permissions(__file__, test_file)
                self.assertFalse(success)
            finally:
                # 恢复权限以便清理
                os.chmod(readonly_dir, 0o755)


def run_comprehensive_tests():
    """运行综合测试套件"""
    print("🧪 开始运行综合单元测试套件...")
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加所有测试类
    test_classes = [
        TestTaskOrchestrator,
        TestConfigManager,
        TestFileUtils,
        TestValidationUtils,
        TestEdgeCases
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 统计结果
    total_tests = result.testsRun
    failures = len(result.failures)
    errors = len(result.errors)
    success_rate = (total_tests - failures - errors) / total_tests * 100 if total_tests > 0 else 0
    
    print(f"\n📊 测试结果统计:")
    print(f"   总测试数: {total_tests}")
    print(f"   成功: {total_tests - failures - errors}")
    print(f"   失败: {failures}")
    print(f"   错误: {errors}")
    print(f"   成功率: {success_rate:.1f}%")
    
    if failures > 0:
        print(f"\n❌ 失败的测试:")
        for test, traceback in result.failures:
            print(f"   - {test}: {traceback.split('AssertionError:')[-1].strip()}")
    
    if errors > 0:
        print(f"\n💥 错误的测试:")
        for test, traceback in result.errors:
            print(f"   - {test}: {traceback.split('Exception:')[-1].strip()}")
    
    if failures == 0 and errors == 0:
        print(f"\n🎉 所有测试通过！代码质量良好")
    
    return success_rate >= 90.0


if __name__ == "__main__":
    success = run_comprehensive_tests()
    sys.exit(0 if success else 1)
