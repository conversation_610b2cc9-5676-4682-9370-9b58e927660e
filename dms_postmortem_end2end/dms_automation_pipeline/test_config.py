#!/usr/bin/env python3
"""
配置管理器测试脚本

用于验证ConfigManager的功能
"""

import sys
import os
import json
import tempfile

# 添加父目录到Python路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

from dms_automation_pipeline.config.config_manager import ConfigManager, ConfigValidationError


def test_config_loading():
    """测试配置加载"""
    print("=== 测试配置加载功能 ===")
    
    config_manager = ConfigManager()
    
    # 测试加载模板配置
    template_config = config_manager.get_template_config()
    print(f"✓ 模板配置加载成功，包含 {len(template_config)} 个顶级配置项")
    assert len(template_config) > 0
    assert "project_info" in template_config
    assert "video_processing" in template_config
    
    return True


def test_config_validation():
    """测试配置验证"""
    print("\n=== 测试配置验证功能 ===")
    
    config_manager = ConfigManager()
    
    # 测试有效配置
    valid_config = {
        "project_info": {
            "name": "测试项目",
            "version": "1.0.0"
        },
        "video_processing": {
            "enabled": True,
            "input_directory": "./input/",
            "output_directory": "./output/"
        },
        "environment": {
            "enabled": True,
            "cpp_source_directory": "./cpp/",
            "runtime_directory": "./runtime/"
        },
        "remote": {
            "enabled": True,
            "ssh": {
                "host": "***********",
                "username": "test"
            },
            "service": {
                "path": "/test/path",
                "port": 1180
            }
        },
        "rendering": {
            "enabled": True,
            "cpp_program": "./test_program",
            "output_directory": "./output/"
        }
    }
    
    try:
        result = config_manager.validate_config(valid_config)
        print("✓ 有效配置验证通过")
        assert result == True
    except ConfigValidationError as e:
        print(f"⚠️ 配置验证失败（可能是jsonschema未安装）: {e}")
    
    # 测试无效配置
    invalid_config = {
        "project_info": {
            "name": ""  # 空名称应该失败
        }
    }
    
    try:
        config_manager.validate_config(invalid_config)
        print("❌ 无效配置应该验证失败")
        return False
    except ConfigValidationError:
        print("✓ 无效配置正确被拒绝")
    
    return True


def test_config_merge():
    """测试配置合并"""
    print("\n=== 测试配置合并功能 ===")
    
    config_manager = ConfigManager()
    
    base_config = {
        "project_info": {
            "name": "基础项目",
            "version": "1.0.0"
        },
        "video_processing": {
            "enabled": True,
            "max_parallel": 2
        }
    }
    
    override_config = {
        "project_info": {
            "name": "覆盖项目"  # 这个会被覆盖
        },
        "video_processing": {
            "max_parallel": 4  # 这个会被覆盖
        },
        "new_section": {
            "new_value": "新增内容"  # 这个会被添加
        }
    }
    
    merged = config_manager.merge_configs(base_config, override_config)
    
    print(f"✓ 配置合并成功")
    assert merged["project_info"]["name"] == "覆盖项目"
    assert merged["project_info"]["version"] == "1.0.0"  # 保持原值
    assert merged["video_processing"]["max_parallel"] == 4
    assert merged["video_processing"]["enabled"] == True  # 保持原值
    assert merged["new_section"]["new_value"] == "新增内容"
    
    return True


def test_config_save_load():
    """测试配置保存和加载"""
    print("\n=== 测试配置保存和加载功能 ===")
    
    config_manager = ConfigManager()
    
    test_config = {
        "project_info": {
            "name": "测试保存项目",
            "version": "1.0.0"
        },
        "video_processing": {
            "enabled": True,
            "input_directory": "./test_input/",
            "output_directory": "./test_output/"
        },
        "environment": {
            "enabled": True,
            "cpp_source_directory": "./test_cpp/",
            "runtime_directory": "./test_runtime/"
        },
        "remote": {
            "enabled": True,
            "ssh": {
                "host": "*************",
                "username": "testuser"
            },
            "service": {
                "path": "/test/service",
                "port": 1180
            }
        },
        "rendering": {
            "enabled": True,
            "cpp_program": "./test_program",
            "output_directory": "./test_output/"
        }
    }
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        temp_file = f.name
    
    try:
        # 保存配置
        config_manager.save_config(test_config, temp_file)
        print("✓ 配置保存成功")
        
        # 加载配置
        loaded_config = config_manager.load_config(temp_file)
        print("✓ 配置加载成功")
        
        # 验证内容一致
        assert loaded_config["project_info"]["name"] == "测试保存项目"
        assert loaded_config["video_processing"]["enabled"] == True
        print("✓ 保存和加载的内容一致")
        
    finally:
        # 清理临时文件
        if os.path.exists(temp_file):
            os.remove(temp_file)
    
    return True


def test_error_handling():
    """测试错误处理"""
    print("\n=== 测试错误处理功能 ===")
    
    config_manager = ConfigManager()
    
    # 测试加载不存在的文件
    try:
        config_manager.load_config("nonexistent_file.json")
        print("❌ 应该抛出异常")
        return False
    except ConfigValidationError:
        print("✓ 正确处理文件不存在错误")
    
    # 测试加载无效JSON
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        f.write("{ invalid json content")
        invalid_json_file = f.name
    
    try:
        config_manager.load_config(invalid_json_file)
        print("❌ 应该抛出JSON格式错误")
        return False
    except ConfigValidationError:
        print("✓ 正确处理JSON格式错误")
    finally:
        os.remove(invalid_json_file)
    
    return True


def main():
    """主测试函数"""
    try:
        print("开始测试ConfigManager...")
        
        # 运行所有测试
        tests = [
            test_config_loading,
            test_config_validation,
            test_config_merge,
            test_config_save_load,
            test_error_handling
        ]
        
        for test in tests:
            if not test():
                print(f"❌ 测试失败: {test.__name__}")
                return False
                
        print("\n🎉 所有配置管理器测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
