{"current_state": "COMPLETED", "stage_results": {"RENDERING": {"success": true, "message": "渲染完成: 成功 3/3 个任务", "data": {"batch_id": "batch_1752544090176", "batch_result": {"batch_id": "batch_1752544090176", "total_videos": 3, "submitted_tasks": ["render_test_video_2_1752544090176", "render_test_video_0_1752544090176", "render_test_video_1_1752544090176"], "completed_tasks": 3, "failed_tasks": 0, "start_time": 1752544090.1762922, "end_time": 1752544092.1773932, "status": "completed", "running_tasks": 0, "pending_tasks": 0, "progress": 100.0}, "collected_results": {"batch_id": "batch_1752544090176", "batch_info": {"batch_id": "batch_1752544090176", "total_videos": 3, "submitted_tasks": ["render_test_video_2_1752544090176", "render_test_video_0_1752544090176", "render_test_video_1_1752544090176"], "completed_tasks": 3, "failed_tasks": 0, "start_time": 1752544090.1762922, "end_time": 1752544092.1773932, "status": "completed", "running_tasks": 0, "pending_tasks": 0, "progress": 100.0}, "task_results": [{"task_id": "render_test_video_2_1752544090176", "video_path": "/tmp/tmpza9kfpa8/test_video_2.mp4", "output_path": "/tmp/tmpza9kfpa8/output/test_video_2_rendered.mp4", "status": "completed", "created_time": 1752544090.176308, "start_time": 1752544091.1772745, "end_time": 1752544091.1774976, "duration": 0.0002231597900390625, "retry_count": 0, "error_message": null, "task_result": {"success": true, "message": "渲染完成", "output_path": "/tmp/tmpza9kfpa8/output/test_video_2_rendered.mp4", "duration": 2.9325485229492188e-05, "details": {"render_time": 1.0, "output_size": 1024}}, "verification": {"file_exists": false, "file_size": 0, "file_readable": false, "valid": false}, "statistics": {"input_file_size": 20, "output_file_size": 0, "processing_time": 0.0002231597900390625, "throughput": 0.08547008547008547, "success_rate": 1.0}}, {"task_id": "render_test_video_0_1752544090176", "video_path": "/tmp/tmpza9kfpa8/test_video_0.mp4", "output_path": "/tmp/tmpza9kfpa8/output/test_video_0_rendered.mp4", "status": "completed", "created_time": 1752544090.1763427, "start_time": 1752544091.1775227, "end_time": 1752544091.1775925, "duration": 6.985664367675781e-05, "retry_count": 0, "error_message": null, "task_result": {"success": true, "message": "渲染完成", "output_path": "/tmp/tmpza9kfpa8/output/test_video_0_rendered.mp4", "duration": 1.1205673217773438e-05, "details": {"render_time": 1.0, "output_size": 1024}}, "verification": {"file_exists": false, "file_size": 0, "file_readable": false, "valid": false}, "statistics": {"input_file_size": 20, "output_file_size": 0, "processing_time": 6.985664367675781e-05, "throughput": 0.27303754266211605, "success_rate": 1.0}}, {"task_id": "render_test_video_1_1752544090176", "video_path": "/tmp/tmpza9kfpa8/test_video_1.mp4", "output_path": "/tmp/tmpza9kfpa8/output/test_video_1_rendered.mp4", "status": "completed", "created_time": 1752544090.1763659, "start_time": 1752544091.1775358, "end_time": 1752544091.1776798, "duration": 0.00014400482177734375, "retry_count": 0, "error_message": null, "task_result": {"success": true, "message": "渲染完成", "output_path": "/tmp/tmpza9kfpa8/output/test_video_1_rendered.mp4", "duration": 9.298324584960938e-06, "details": {"render_time": 1.0, "output_size": 1024}}, "verification": {"file_exists": false, "file_size": 0, "file_readable": false, "valid": false}, "statistics": {"input_file_size": 20, "output_file_size": 0, "processing_time": 0.00014400482177734375, "throughput": 0.13245033112582782, "success_rate": 1.0}}], "summary": {"total_tasks": 3, "completed_tasks": 3, "failed_tasks": 0, "total_duration": 0.00043702125549316406, "average_duration": 0.0001456737518310547, "total_input_size": 60, "total_output_size": 0, "average_throughput": 0.16365265308600976, "success_rate": 1.0, "verification_passed": 0, "verification_failed": 3}, "collection_time": 1752544092.1775053, "report_path": "/tmp/tmpza9kfpa8/results/batch_report_batch_1752544090176_20250715_094812.json"}, "summary": {"total_tasks": 3, "completed_tasks": 3, "failed_tasks": 0, "total_duration": 0.00043702125549316406, "average_duration": 0.0001456737518310547, "total_input_size": 60, "total_output_size": 0, "average_throughput": 0.16365265308600976, "success_rate": 1.0, "verification_passed": 0, "verification_failed": 3}, "video_files_processed": 3, "success_rate": 1.0, "report_path": "/tmp/tmpza9kfpa8/results/batch_report_batch_1752544090176_20250715_094812.json"}, "error": null, "timestamp": "2025-07-15T09:48:12.177854"}}, "last_updated": "2025-07-15T09:48:12.177953"}