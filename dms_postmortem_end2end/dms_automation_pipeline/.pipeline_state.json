{"current_state": "COMPLETED", "stage_results": {"RENDERING": {"success": true, "message": "渲染完成: 成功 3/3 个任务", "data": {"batch_id": "batch_1752490474414", "batch_result": {"batch_id": "batch_1752490474414", "total_videos": 3, "submitted_tasks": ["render_test_video_2_1752490474414", "render_test_video_0_1752490474414", "render_test_video_1_1752490474414"], "completed_tasks": 3, "failed_tasks": 0, "start_time": 1752490474.414148, "end_time": 1752490475.4152734, "status": "completed", "running_tasks": 0, "pending_tasks": 0, "progress": 100.0}, "collected_results": {"batch_id": "batch_1752490474414", "batch_info": {"batch_id": "batch_1752490474414", "total_videos": 3, "submitted_tasks": ["render_test_video_2_1752490474414", "render_test_video_0_1752490474414", "render_test_video_1_1752490474414"], "completed_tasks": 3, "failed_tasks": 0, "start_time": 1752490474.414148, "end_time": 1752490475.4152734, "status": "completed", "running_tasks": 0, "pending_tasks": 0, "progress": 100.0}, "task_results": [{"task_id": "render_test_video_2_1752490474414", "video_path": "/tmp/tmp4gm0m490/test_video_2.mp4", "output_path": "/tmp/tmp4gm0m490/output/test_video_2_rendered.mp4", "status": "completed", "created_time": 1752490474.4141638, "start_time": 1752490475.4142232, "end_time": 1752490475.4143958, "duration": 0.00017261505126953125, "retry_count": 0, "error_message": null, "task_result": {"success": true, "message": "渲染完成", "output_path": "/tmp/tmp4gm0m490/output/test_video_2_rendered.mp4", "duration": 2.86102294921875e-05, "details": {"render_time": 1.0, "output_size": 1024}}, "verification": {"file_exists": false, "file_size": 0, "file_readable": false, "valid": false}, "statistics": {"input_file_size": 20, "output_file_size": 0, "processing_time": 0.00017261505126953125, "throughput": 0.11049723756906077, "success_rate": 1.0}}, {"task_id": "render_test_video_0_1752490474414", "video_path": "/tmp/tmp4gm0m490/test_video_0.mp4", "output_path": "/tmp/tmp4gm0m490/output/test_video_0_rendered.mp4", "status": "completed", "created_time": 1752490474.4141982, "start_time": 1752490475.4144168, "end_time": 1752490475.4145393, "duration": 0.00012254714965820312, "retry_count": 0, "error_message": null, "task_result": {"success": true, "message": "渲染完成", "output_path": "/tmp/tmp4gm0m490/output/test_video_0_rendered.mp4", "duration": 1.3828277587890625e-05, "details": {"render_time": 1.0, "output_size": 1024}}, "verification": {"file_exists": false, "file_size": 0, "file_readable": false, "valid": false}, "statistics": {"input_file_size": 20, "output_file_size": 0, "processing_time": 0.00012254714965820312, "throughput": 0.1556420233463035, "success_rate": 1.0}}, {"task_id": "render_test_video_1_1752490474414", "video_path": "/tmp/tmp4gm0m490/test_video_1.mp4", "output_path": "/tmp/tmp4gm0m490/output/test_video_1_rendered.mp4", "status": "completed", "created_time": 1752490474.4142215, "start_time": 1752490475.4144287, "end_time": 1752490475.4145596, "duration": 0.0001308917999267578, "retry_count": 0, "error_message": null, "task_result": {"success": true, "message": "渲染完成", "output_path": "/tmp/tmp4gm0m490/output/test_video_1_rendered.mp4", "duration": 7.3909759521484375e-06, "details": {"render_time": 1.0, "output_size": 1024}}, "verification": {"file_exists": false, "file_size": 0, "file_readable": false, "valid": false}, "statistics": {"input_file_size": 20, "output_file_size": 0, "processing_time": 0.0001308917999267578, "throughput": 0.14571948998178508, "success_rate": 1.0}}], "summary": {"total_tasks": 3, "completed_tasks": 3, "failed_tasks": 0, "total_duration": 0.0004260540008544922, "average_duration": 0.00014201800028483072, "total_input_size": 60, "total_output_size": 0, "average_throughput": 0.13728625029904978, "success_rate": 1.0, "verification_passed": 0, "verification_failed": 3}, "collection_time": 1752490475.4153535, "report_path": "/tmp/tmp4gm0m490/results/batch_report_batch_1752490474414_20250714_185435.json"}, "summary": {"total_tasks": 3, "completed_tasks": 3, "failed_tasks": 0, "total_duration": 0.0004260540008544922, "average_duration": 0.00014201800028483072, "total_input_size": 60, "total_output_size": 0, "average_throughput": 0.13728625029904978, "success_rate": 1.0, "verification_passed": 0, "verification_failed": 3}, "video_files_processed": 3, "success_rate": 1.0, "report_path": "/tmp/tmp4gm0m490/results/batch_report_batch_1752490474414_20250714_185435.json"}, "error": null, "timestamp": "2025-07-14T18:54:35.415608"}}, "last_updated": "2025-07-14T18:54:35.415684"}