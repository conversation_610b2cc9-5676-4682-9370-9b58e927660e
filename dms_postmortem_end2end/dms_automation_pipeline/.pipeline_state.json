{"current_state": "REMOTE_VALIDATION", "stage_results": {"ENV_SETUP": {"success": true, "message": "环境设置完成: 部署5个文件", "data": {"deployment_result": {"success": true, "message": "成功部署所有文件: 5个", "deployed_files": ["test_dms_internal_postmortem", "libtx_dms.so", "FaceDetection.ovm", "FaceKeypoints.ovm", "eye.ovm"], "failed_files": [], "deployment_time": 0.00032448768615722656}, "integrity_result": {"all_files_present": true, "permissions_correct": true, "integrity_verified": true, "missing_files": [], "permission_issues": [], "integrity_issues": []}, "config_results": {"ip_port.json": {"confirmation": "confirm"}, "calidata.json": {"confirmation": "confirm"}}, "validation_result": {"all_files_exist": true, "permissions_correct": true, "configs_valid": true, "missing_files": [], "permission_issues": [], "config_issues": [], "summary": "环境验证通过"}, "environment_status": {"cpp_source_directory": "/tmp/tmpkrfydfmt/cpp_source", "runtime_directory": "/tmp/tmpkrfydfmt/runtime", "required_files": ["test_dms_internal_postmortem", "libtx_dms.so", "FaceDetection.ovm", "FaceKeypoints.ovm", "eye.ovm"], "config_files": ["ip_port.json", "calidata.json"], "source_directory_exists": true, "runtime_directory_exists": true, "deployed_files": [{"name": "test_dms_internal_postmortem", "size": 41, "executable": true}, {"name": "libtx_dms.so", "size": 25, "executable": false}, {"name": "FaceDetection.ovm", "size": 30, "executable": false}, {"name": "FaceKeypoints.ovm", "size": 30, "executable": false}, {"name": "eye.ovm", "size": 20, "executable": false}], "missing_files": [], "config_status": {"ip_port.json": {"exists": true, "valid": true, "errors": [], "size": 35}, "calidata.json": {"exists": true, "valid": true, "errors": [], "size": 80}}}, "deployed_files_count": 5, "config_files_processed": 2}, "error": null, "timestamp": "2025-07-14T18:33:52.073387"}}, "last_updated": "2025-07-14T18:33:52.073401"}