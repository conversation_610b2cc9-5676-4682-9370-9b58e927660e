#!/usr/bin/env python3
"""
DMS自动化分析与渲染平台 - 部署验证脚本

在目标环境中进行完整的部署验证和最终验收
"""

import sys
import os
import time
import json
import subprocess
import tempfile
from pathlib import Path
from datetime import datetime


class DeploymentVerifier:
    """部署验证器"""
    
    def __init__(self):
        self.verification_results = {}
        self.start_time = time.time()
        
    def log_result(self, test_name, success, message, details=None):
        """记录验证结果"""
        self.verification_results[test_name] = {
            "success": success,
            "message": message,
            "details": details or {},
            "timestamp": time.time()
        }
        
        status = "✅" if success else "❌"
        print(f"{status} {test_name}: {message}")
        
    def verify_system_requirements(self):
        """验证系统要求"""
        print("\n🔍 验证系统要求...")
        
        # 检查Python版本
        python_version = sys.version_info
        if python_version >= (3, 7):
            self.log_result("Python版本", True, f"Python {python_version.major}.{python_version.minor}.{python_version.micro}")
        else:
            self.log_result("Python版本", False, f"Python版本过低: {python_version.major}.{python_version.minor}")
            
        # 检查FFmpeg
        try:
            result = subprocess.run(['ffmpeg', '-version'], capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                version_line = result.stdout.split('\n')[0]
                self.log_result("FFmpeg", True, f"已安装: {version_line}")
            else:
                self.log_result("FFmpeg", False, "FFmpeg执行失败")
        except (subprocess.TimeoutExpired, FileNotFoundError):
            self.log_result("FFmpeg", False, "FFmpeg未找到或执行超时")
            
        # 检查Python依赖
        required_packages = ['paramiko', 'rich', 'jsonschema']
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package)
                self.log_result(f"Python包-{package}", True, "已安装")
            except ImportError:
                missing_packages.append(package)
                self.log_result(f"Python包-{package}", False, "未安装")
        
        return len(missing_packages) == 0
        
    def verify_file_structure(self):
        """验证文件结构"""
        print("\n📁 验证文件结构...")
        
        required_files = [
            "dms_automation_main.py",
            "quick_start.py",
            "core/task_orchestrator.py",
            "core/config_manager.py",
            "utils/file_utils.py",
            "utils/validation_utils.py",
            "utils/logger_config.py",
            "config/pipeline_config.json"
        ]
        
        required_dirs = [
            "core",
            "utils", 
            "config",
            "logs",
            "input_videos",
            "output",
            "results"
        ]
        
        all_files_exist = True
        
        # 检查文件
        for file_path in required_files:
            if os.path.exists(file_path):
                self.log_result(f"文件-{file_path}", True, "存在")
            else:
                self.log_result(f"文件-{file_path}", False, "缺失")
                all_files_exist = False
                
        # 检查目录
        for dir_path in required_dirs:
            if os.path.exists(dir_path) and os.path.isdir(dir_path):
                self.log_result(f"目录-{dir_path}", True, "存在")
            else:
                self.log_result(f"目录-{dir_path}", False, "缺失")
                all_files_exist = False
                
        return all_files_exist
        
    def verify_configuration(self):
        """验证配置文件"""
        print("\n⚙️ 验证配置文件...")
        
        config_file = "config/pipeline_config.json"
        
        # 检查配置文件存在性
        if not os.path.exists(config_file):
            self.log_result("配置文件存在性", False, f"配置文件不存在: {config_file}")
            return False
            
        # 检查JSON格式
        try:
            with open(config_file, 'r') as f:
                config = json.load(f)
            self.log_result("JSON格式", True, "配置文件JSON格式正确")
        except json.JSONDecodeError as e:
            self.log_result("JSON格式", False, f"JSON格式错误: {e}")
            return False
            
        # 检查必需字段
        required_sections = ["video_processing", "environment", "remote", "rendering"]
        missing_sections = []
        
        for section in required_sections:
            if section in config:
                self.log_result(f"配置节-{section}", True, "存在")
            else:
                missing_sections.append(section)
                self.log_result(f"配置节-{section}", False, "缺失")
                
        # 验证配置逻辑
        try:
            # 直接导入配置管理器模块
            import importlib.util
            spec = importlib.util.spec_from_file_location("config_manager", "core/config_manager.py")
            config_manager_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(config_manager_module)

            config_manager = config_manager_module.ConfigManager()
            is_valid, error_msg = config_manager.validate_config(config)

            if is_valid:
                self.log_result("配置验证", True, "配置验证通过")
            else:
                self.log_result("配置验证", False, f"配置验证失败: {error_msg}")

        except Exception as e:
            self.log_result("配置验证", False, f"配置验证异常: {e}")
            
        return len(missing_sections) == 0
        
    def verify_permissions(self):
        """验证文件权限"""
        print("\n🔐 验证文件权限...")
        
        # 检查主程序执行权限
        main_files = ["dms_automation_main.py", "quick_start.py"]
        
        for file_path in main_files:
            if os.path.exists(file_path):
                if os.access(file_path, os.R_OK):
                    self.log_result(f"权限-{file_path}", True, "可读")
                else:
                    self.log_result(f"权限-{file_path}", False, "不可读")
            else:
                self.log_result(f"权限-{file_path}", False, "文件不存在")
                
        # 检查目录写权限
        write_dirs = ["logs", "output", "results", "temp"]
        
        for dir_path in write_dirs:
            if os.path.exists(dir_path):
                if os.access(dir_path, os.W_OK):
                    self.log_result(f"写权限-{dir_path}", True, "可写")
                else:
                    self.log_result(f"写权限-{dir_path}", False, "不可写")
            else:
                # 尝试创建目录
                try:
                    os.makedirs(dir_path, exist_ok=True)
                    self.log_result(f"写权限-{dir_path}", True, "目录已创建")
                except Exception as e:
                    self.log_result(f"写权限-{dir_path}", False, f"无法创建: {e}")
                    
        return True
        
    def verify_basic_functionality(self):
        """验证基本功能"""
        print("\n🧪 验证基本功能...")
        
        # 测试配置加载
        try:
            # 直接测试配置管理器
            import importlib.util
            spec = importlib.util.spec_from_file_location("config_manager", "core/config_manager.py")
            config_manager_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(config_manager_module)

            cm = config_manager_module.ConfigManager()
            self.log_result("配置加载", True, "配置管理器工作正常")

        except Exception as e:
            self.log_result("配置加载", False, f"配置加载异常: {e}")
            
        # 测试依赖检查
        try:
            result = subprocess.run([
                sys.executable, 'quick_start.py', '--check-deps'
            ], capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                self.log_result("依赖检查", True, "依赖检查通过")
            else:
                self.log_result("依赖检查", False, f"依赖检查失败: {result.stderr}")
                
        except Exception as e:
            self.log_result("依赖检查", False, f"依赖检查异常: {e}")
            
        # 测试简单演示
        try:
            result = subprocess.run([
                sys.executable, '-c',
                "print('Basic functionality test passed')"
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                self.log_result("基本功能", True, "Python环境正常")
            else:
                self.log_result("基本功能", False, "Python环境异常")
                
        except Exception as e:
            self.log_result("基本功能", False, f"基本功能测试异常: {e}")
            
        return True
        
    def verify_integration(self):
        """验证集成功能"""
        print("\n🔗 验证集成功能...")
        
        # 创建临时测试环境
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建测试视频文件
            test_video = os.path.join(temp_dir, "test.mp4")
            with open(test_video, 'w') as f:
                f.write("fake video content")
                
            # 测试文件操作
            try:
                # 直接导入文件工具模块
                import importlib.util
                spec = importlib.util.spec_from_file_location("file_utils", "utils/file_utils.py")
                file_utils_module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(file_utils_module)

                if file_utils_module.is_file_exists_and_readable(test_video):
                    self.log_result("文件工具", True, "文件操作功能正常")
                else:
                    self.log_result("文件工具", False, "文件操作功能异常")

                # 测试MD5计算
                md5_hash = file_utils_module.calculate_md5(test_video)
                if md5_hash and len(md5_hash) == 32:
                    self.log_result("MD5计算", True, "MD5计算功能正常")
                else:
                    self.log_result("MD5计算", False, "MD5计算功能异常")

            except Exception as e:
                self.log_result("文件工具", False, f"文件工具异常: {e}")

        # 测试验证工具
        try:
            # 直接导入验证工具模块
            import importlib.util
            spec = importlib.util.spec_from_file_location("validation_utils", "utils/validation_utils.py")
            validation_utils_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(validation_utils_module)

            if validation_utils_module.validate_ip_address("***********") and validation_utils_module.validate_port(8080):
                self.log_result("验证工具", True, "验证工具功能正常")
            else:
                self.log_result("验证工具", False, "验证工具功能异常")

        except Exception as e:
            self.log_result("验证工具", False, f"验证工具异常: {e}")
            
        return True
        
    def verify_performance(self):
        """验证性能"""
        print("\n⚡ 验证性能...")
        
        # 测试启动时间
        start_time = time.time()
        try:
            # 简化的启动测试
            startup_time = time.time() - start_time

            if startup_time < 1:
                self.log_result("启动性能", True, f"启动时间: {startup_time:.2f}秒")
            else:
                self.log_result("启动性能", False, f"启动时间过长: {startup_time:.2f}秒")

        except Exception as e:
            self.log_result("启动性能", False, f"启动性能测试异常: {e}")
            
        # 测试内存使用
        try:
            import psutil
            process = psutil.Process()
            memory_mb = process.memory_info().rss / 1024 / 1024
            
            if memory_mb < 200:  # 小于200MB
                self.log_result("内存使用", True, f"内存使用: {memory_mb:.1f}MB")
            else:
                self.log_result("内存使用", False, f"内存使用过高: {memory_mb:.1f}MB")
                
        except Exception as e:
            self.log_result("内存使用", False, f"内存测试异常: {e}")
            
        return True
        
    def generate_verification_report(self):
        """生成验证报告"""
        print("\n📄 生成验证报告...")
        
        total_tests = len(self.verification_results)
        passed_tests = sum(1 for result in self.verification_results.values() if result["success"])
        failed_tests = total_tests - passed_tests
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        report = {
            "verification_timestamp": datetime.now().isoformat(),
            "total_verification_time": time.time() - self.start_time,
            "summary": {
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "failed_tests": failed_tests,
                "success_rate": success_rate
            },
            "test_results": self.verification_results,
            "deployment_status": "PASSED" if failed_tests == 0 else "FAILED",
            "recommendations": self._generate_recommendations()
        }
        
        # 保存报告
        report_file = f"results/deployment_verification_report_{int(time.time())}.json"
        os.makedirs("results", exist_ok=True)
        
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            print(f"✅ 验证报告已保存: {report_file}")
        except Exception as e:
            print(f"❌ 保存验证报告失败: {e}")
            
        return report
        
    def _generate_recommendations(self):
        """生成建议"""
        recommendations = []
        
        for test_name, result in self.verification_results.items():
            if not result["success"]:
                if "Python版本" in test_name:
                    recommendations.append("升级Python到3.7或更高版本")
                elif "FFmpeg" in test_name:
                    recommendations.append("安装FFmpeg: sudo apt install ffmpeg")
                elif "Python包" in test_name:
                    package = test_name.split('-')[1]
                    recommendations.append(f"安装Python包: pip install {package}")
                elif "文件" in test_name or "目录" in test_name:
                    recommendations.append("检查文件结构，运行 python quick_start.py --setup")
                elif "权限" in test_name:
                    recommendations.append("检查文件权限，运行 chmod +x *.py")
                elif "配置" in test_name:
                    recommendations.append("检查配置文件，运行 python quick_start.py --create-config")
                    
        return list(set(recommendations))  # 去重


def run_deployment_verification():
    """运行部署验证"""
    print("🚀 DMS自动化分析与渲染平台 - 部署验证")
    print("=" * 60)
    
    verifier = DeploymentVerifier()
    
    try:
        # 执行各项验证
        verification_steps = [
            ("系统要求", verifier.verify_system_requirements),
            ("文件结构", verifier.verify_file_structure),
            ("配置文件", verifier.verify_configuration),
            ("文件权限", verifier.verify_permissions),
            ("基本功能", verifier.verify_basic_functionality),
            ("集成功能", verifier.verify_integration),
            ("性能测试", verifier.verify_performance)
        ]
        
        all_passed = True
        
        for step_name, step_func in verification_steps:
            try:
                step_result = step_func()
                if not step_result:
                    all_passed = False
            except Exception as e:
                print(f"❌ {step_name}验证异常: {e}")
                all_passed = False
                
        # 生成报告
        report = verifier.generate_verification_report()
        
        # 显示总结
        print(f"\n📊 验证总结")
        print("=" * 40)
        print(f"总测试数: {report['summary']['total_tests']}")
        print(f"通过测试: {report['summary']['passed_tests']}")
        print(f"失败测试: {report['summary']['failed_tests']}")
        print(f"成功率: {report['summary']['success_rate']:.1f}%")
        print(f"部署状态: {report['deployment_status']}")
        
        if report["recommendations"]:
            print(f"\n💡 建议:")
            for rec in report["recommendations"]:
                print(f"   • {rec}")
        
        if report["deployment_status"] == "PASSED":
            print(f"\n🎉 部署验证通过！平台已准备就绪")
            return True
        else:
            print(f"\n⚠️  部署验证失败，请根据建议修复问题")
            return False
            
    except Exception as e:
        print(f"\n❌ 部署验证过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🎯 DMS自动化分析与渲染平台 - 部署验证工具")
    print("=" * 60)
    print("此工具将验证平台的部署状态和功能完整性")
    print("=" * 60)
    
    try:
        success = run_deployment_verification()
        
        if success:
            print("\n✅ 部署验证成功完成！")
            print("🚀 平台已准备就绪，可以开始使用")
            print("\n📚 下一步:")
            print("   1. 查看用户手册: USER_MANUAL.md")
            print("   2. 运行功能演示: python simple_demo.py")
            print("   3. 开始使用平台: python quick_start.py --run")
        else:
            print("\n❌ 部署验证失败！")
            print("🔧 请根据上述建议修复问题后重新运行验证")
            
        return success
        
    except KeyboardInterrupt:
        print("\n\n⏹️  验证被用户中断")
        return False
    except Exception as e:
        print(f"\n❌ 验证过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
