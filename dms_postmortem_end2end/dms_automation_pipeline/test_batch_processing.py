#!/usr/bin/env python3
"""
批量处理测试脚本

验证VideoPreprocessor的批量处理功能
"""

import sys
import os
import tempfile
import time
from unittest.mock import patch, MagicMock

# 添加父目录到Python路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

from dms_automation_pipeline.core.video_preprocessor import VideoPreprocessor, VideoProcessingResult


def create_test_config(output_dir: str) -> dict:
    """创建测试配置"""
    return {
        "video_processing": {
            "input_directory": "./input_videos/",
            "output_directory": output_dir,
            "supported_formats": [".mkv", ".mp4", ".avi"],
            "deduplication": {
                "enabled": True,
                "force_reprocess": False,
                "naming_pattern": "{base_name}_{time_range}_roi_{roi}.mp4"
            },
            "batch_processing": {
                "max_parallel": 2,  # 减少并发数以便测试
                "retry_count": 1,
                "timeout_seconds": 30
            }
        }
    }


def test_batch_processing_basic():
    """测试基本批量处理功能"""
    print("=== 测试基本批量处理功能 ===")

    with tempfile.TemporaryDirectory() as temp_dir:
        config = create_test_config(temp_dir)
        processor = VideoPreprocessor(config)

        # 创建测试视频文件
        video1_path = os.path.join(temp_dir, "video1.mkv")
        video2_path = os.path.join(temp_dir, "video2.mkv")

        with open(video1_path, 'w') as f:
            f.write("fake video content")
        with open(video2_path, 'w') as f:
            f.write("fake video content")

        # 模拟输入验证通过
        with patch.object(processor, 'validate_inputs') as mock_validate:
            mock_validate.return_value = (True, "")

            # 模拟FFmpeg执行成功
            with patch.object(processor.ffmpeg_wrapper, 'execute_ffmpeg_command') as mock_ffmpeg:
                # 模拟输出文件创建
                def create_output_file(*args, **kwargs):
                    # 获取输出文件路径（从FFmpeg命令中提取）
                    cmd = args[0]
                    output_file = cmd[-1]  # FFmpeg命令的最后一个参数是输出文件
                    os.makedirs(os.path.dirname(output_file), exist_ok=True)
                    with open(output_file, 'w') as f:
                        f.write("test video content")
                    return (True, "")

                mock_ffmpeg.side_effect = create_output_file

                # 准备批量配置
                batch_config = [
                    {
                        "path": video1_path,
                        "time_ranges": [("00:04:57", "00:05:17")],
                        "roi": "1920:1080:0:0"
                    },
                    {
                        "path": video2_path,
                        "time_ranges": [("00:01:00", "00:01:20"), ("00:02:00", "00:02:20")],
                        "roi": "1280:720:0:0"
                    }
                ]

                # 执行批量处理
                result = processor.process_batch(batch_config)

                print(f"✓ 批量处理结果: {result.success}")
                print(f"✓ 处理消息: {result.message}")
                print(f"✓ 输出文件数: {len(result.output_files)}")
                print(f"✓ 处理时间: {result.processing_time:.2f}秒")

                assert result.success == True
                assert len(result.output_files) == 3  # 总共3个片段
                assert result.processing_time > 0

                # 验证FFmpeg被调用了正确的次数
                assert mock_ffmpeg.call_count == 3

                print("✓ 基本批量处理功能测试通过")

    return True


def test_batch_processing_with_failures():
    """测试带失败的批量处理"""
    print("\n=== 测试带失败的批量处理 ===")

    with tempfile.TemporaryDirectory() as temp_dir:
        config = create_test_config(temp_dir)
        processor = VideoPreprocessor(config)

        # 创建测试视频文件
        video_paths = []
        for i in range(1, 4):
            video_path = os.path.join(temp_dir, f"video{i}.mkv")
            with open(video_path, 'w') as f:
                f.write("fake video content")
            video_paths.append(video_path)

        # 模拟输入验证通过
        with patch.object(processor, 'validate_inputs') as mock_validate:
            mock_validate.return_value = (True, "")

            # 模拟部分FFmpeg执行失败
            call_count = 0
            def mock_ffmpeg_with_failures(*args, **kwargs):
                nonlocal call_count
                call_count += 1

                if call_count == 2:  # 第二次调用失败
                    return (False, "模拟FFmpeg失败")
                else:
                    # 成功的情况，创建输出文件
                    cmd = args[0]
                    output_file = cmd[-1]
                    os.makedirs(os.path.dirname(output_file), exist_ok=True)
                    with open(output_file, 'w') as f:
                        f.write("test video content")
                    return (True, "")

            with patch.object(processor.ffmpeg_wrapper, 'execute_ffmpeg_command') as mock_ffmpeg:
                mock_ffmpeg.side_effect = mock_ffmpeg_with_failures

                batch_config = [
                    {
                        "path": video_paths[0],
                        "time_ranges": [("00:04:57", "00:05:17")],
                        "roi": "1920:1080:0:0"
                    },
                    {
                        "path": video_paths[1],
                        "time_ranges": [("00:01:00", "00:01:20")],  # 这个会失败
                        "roi": "1280:720:0:0"
                    },
                    {
                        "path": video_paths[2],
                        "time_ranges": [("00:02:00", "00:02:20")],
                        "roi": "1920:1080:0:0"
                    }
                ]

                result = processor.process_batch(batch_config)

                print(f"✓ 部分失败结果: {result.success}")
                print(f"✓ 处理消息: {result.message}")
                print(f"✓ 成功文件数: {len(result.output_files)}")
                print(f"✓ 错误信息: {result.error}")

                assert result.success == True  # 部分成功也算成功
                assert len(result.output_files) == 2  # 2个成功，1个失败
                assert "video2.mkv" in result.error  # 错误信息中包含失败的视频

                print("✓ 带失败的批量处理测试通过")

    return True


def test_batch_processing_with_deduplication():
    """测试带去重的批量处理"""
    print("\n=== 测试带去重的批量处理 ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config = create_test_config(temp_dir)
        processor = VideoPreprocessor(config)
        
        # 准备批量配置（包含重复的处理请求）
        batch_config = [
            {
                "path": "video1.mkv",
                "time_ranges": [("00:04:57", "00:05:17")],
                "roi": "1920:1080:0:0"
            },
            {
                "path": "video1.mkv",  # 相同的视频和时间范围
                "time_ranges": [("00:04:57", "00:05:17")],
                "roi": "1920:1080:0:0"
            }
        ]
        
        # 第一次处理 - 应该实际执行FFmpeg
        with patch.object(processor.ffmpeg_wrapper, 'execute_ffmpeg_command') as mock_ffmpeg:
            def create_output_file(*args, **kwargs):
                cmd = args[0]
                output_file = cmd[-1]
                os.makedirs(os.path.dirname(output_file), exist_ok=True)
                with open(output_file, 'w') as f:
                    f.write("test video content")
                return (True, "")
            
            mock_ffmpeg.side_effect = create_output_file
            
            result1 = processor.process_batch(batch_config)
            
            print(f"✓ 第一次处理: FFmpeg调用次数 = {mock_ffmpeg.call_count}")
            assert mock_ffmpeg.call_count == 2  # 两个相同的请求都会执行
            
        # 第二次处理 - 应该跳过（去重）
        with patch.object(processor.ffmpeg_wrapper, 'execute_ffmpeg_command') as mock_ffmpeg2:
            mock_ffmpeg2.side_effect = create_output_file
            
            result2 = processor.process_batch(batch_config)
            
            print(f"✓ 第二次处理: FFmpeg调用次数 = {mock_ffmpeg2.call_count}")
            print(f"✓ 跳过的文件数: {len(result2.skipped_files)}")
            
            assert mock_ffmpeg2.call_count == 0  # 应该全部跳过
            assert len(result2.skipped_files) == 2  # 两个文件都被跳过
            assert result2.success == True
            
            print("✓ 带去重的批量处理测试通过")
    
    return True


def test_batch_processing_parallel():
    """测试并行处理"""
    print("\n=== 测试并行处理 ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config = create_test_config(temp_dir)
        config["video_processing"]["batch_processing"]["max_parallel"] = 3
        processor = VideoPreprocessor(config)
        
        # 记录处理时间以验证并行性
        processing_times = []
        
        def slow_ffmpeg_execution(*args, **kwargs):
            start_time = time.time()
            time.sleep(0.1)  # 模拟处理时间
            processing_times.append(time.time() - start_time)
            
            cmd = args[0]
            output_file = cmd[-1]
            os.makedirs(os.path.dirname(output_file), exist_ok=True)
            with open(output_file, 'w') as f:
                f.write("test video content")
            return (True, "")
        
        with patch.object(processor.ffmpeg_wrapper, 'execute_ffmpeg_command') as mock_ffmpeg:
            mock_ffmpeg.side_effect = slow_ffmpeg_execution
            
            batch_config = [
                {
                    "path": f"video{i}.mkv",
                    "time_ranges": [("00:04:57", "00:05:17")],
                    "roi": "1920:1080:0:0"
                }
                for i in range(5)  # 5个视频
            ]
            
            start_time = time.time()
            result = processor.process_batch(batch_config)
            total_time = time.time() - start_time
            
            print(f"✓ 并行处理总时间: {total_time:.2f}秒")
            print(f"✓ 单个处理平均时间: {sum(processing_times)/len(processing_times):.2f}秒")
            print(f"✓ 处理的视频数: {len(result.output_files)}")
            
            # 并行处理应该比串行快
            expected_serial_time = len(batch_config) * 0.1
            assert total_time < expected_serial_time * 0.8  # 至少快20%
            assert result.success == True
            assert len(result.output_files) == 5
            
            print("✓ 并行处理测试通过")
    
    return True


def test_batch_processing_statistics():
    """测试处理统计信息"""
    print("\n=== 测试处理统计信息 ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config = create_test_config(temp_dir)
        processor = VideoPreprocessor(config)
        
        # 创建一些测试文件
        test_files = ["test1.mp4", "test2.mkv", "test3.avi"]
        for filename in test_files:
            file_path = os.path.join(temp_dir, filename)
            with open(file_path, 'w') as f:
                f.write("test content" * 100)  # 创建一些内容
        
        # 获取统计信息
        stats = processor.get_processing_statistics()
        
        print(f"✓ 输出目录: {stats['output_directory']}")
        print(f"✓ 文件总数: {stats['total_files']}")
        print(f"✓ 总大小: {stats['total_size_mb']:.2f} MB")
        print(f"✓ 支持格式: {stats['supported_formats']}")
        print(f"✓ 去重启用: {stats['deduplication_enabled']}")
        print(f"✓ 最大并行数: {stats['max_parallel']}")
        
        assert stats['output_directory'] == temp_dir
        assert stats['total_files'] == len(test_files)
        assert stats['total_size_mb'] > 0
        assert stats['deduplication_enabled'] == True
        assert stats['max_parallel'] == 2
        
        print("✓ 处理统计信息测试通过")
    
    return True


def main():
    """主测试函数"""
    try:
        print("开始测试批量处理功能...")
        
        # 运行所有测试
        tests = [
            test_batch_processing_basic,
            test_batch_processing_with_failures,
            test_batch_processing_with_deduplication,
            test_batch_processing_parallel,
            test_batch_processing_statistics
        ]
        
        for test in tests:
            if not test():
                print(f"❌ 测试失败: {test.__name__}")
                return False
                
        print("\n🎉 所有批量处理测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
