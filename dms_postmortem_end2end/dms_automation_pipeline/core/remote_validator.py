"""
远程验证器模块

负责远程服务相关的验证工作，包括：
- SSH连接管理
- 远程服务管理
- 模型文件同步
- 服务连通性验证
"""

import os
import time
import socket
import threading
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path

try:
    import paramiko
    from paramiko import SSHClient, AutoAddPolicy, RSAKey, AuthenticationException, SSHException
    PARAMIKO_AVAILABLE = True
except ImportError:
    PARAMIKO_AVAILABLE = False
    paramiko = None
    SSHClient = None
    AutoAddPolicy = None
    RSAKey = None
    AuthenticationException = Exception
    SSHException = Exception

try:
    from ..utils.logger_config import setup_module_logger
    from ..utils.file_utils import calculate_md5, is_file_exists_and_readable
    from ..utils.validation_utils import validate_ip_address, validate_port
except ImportError:
    import sys
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from utils.logger_config import setup_module_logger
    from utils.file_utils import calculate_md5, is_file_exists_and_readable
    from utils.validation_utils import validate_ip_address, validate_port


class SSHConnectionResult:
    """SSH连接结果类"""

    def __init__(self, success: bool = False, message: str = "",
                 connection: Optional['SSHConnection'] = None, error: str = None):
        self.success = success
        self.message = message
        self.connection = connection
        self.error = error
        self.connection_time = 0.0

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "success": self.success,
            "message": self.message,
            "error": self.error,
            "connection_time": self.connection_time,
            "connected": self.connection.is_connected() if self.connection else False
        }


class SSHConnection:
    """SSH连接类"""

    def __init__(self, ssh_client: Optional[SSHClient] = None):
        """
        初始化SSH连接

        Args:
            ssh_client: SSH客户端对象
        """
        self.ssh_client = ssh_client
        self.logger = setup_module_logger("SSHConnection")
        self._connected = ssh_client is not None

    def is_connected(self) -> bool:
        """检查连接状态"""
        if not self.ssh_client:
            return False

        try:
            # 尝试执行一个简单命令来检查连接
            transport = self.ssh_client.get_transport()
            return transport is not None and transport.is_active()
        except Exception:
            return False

    def execute_command(self, command: str, timeout: int = 30) -> Dict[str, Any]:
        """
        执行远程命令

        Args:
            command: 要执行的命令
            timeout: 超时时间（秒）

        Returns:
            执行结果字典
        """
        if not self.is_connected():
            return {
                "return_code": -1,
                "stdout": "",
                "stderr": "SSH连接未建立",
                "success": False
            }

        try:
            self.logger.debug(f"执行远程命令: {command}")

            stdin, stdout, stderr = self.ssh_client.exec_command(command, timeout=timeout)

            # 等待命令完成
            exit_status = stdout.channel.recv_exit_status()

            stdout_content = stdout.read().decode('utf-8', errors='ignore')
            stderr_content = stderr.read().decode('utf-8', errors='ignore')

            result = {
                "return_code": exit_status,
                "stdout": stdout_content,
                "stderr": stderr_content,
                "success": exit_status == 0
            }

            if exit_status == 0:
                self.logger.debug(f"命令执行成功: {command}")
            else:
                self.logger.warning(f"命令执行失败: {command}, 退出码: {exit_status}")

            return result

        except Exception as e:
            error_msg = f"执行命令异常: {str(e)}"
            self.logger.error(error_msg)
            return {
                "return_code": -1,
                "stdout": "",
                "stderr": error_msg,
                "success": False
            }

    def upload_file(self, local_path: str, remote_path: str) -> Dict[str, Any]:
        """
        上传文件到远程主机

        Args:
            local_path: 本地文件路径
            remote_path: 远程文件路径

        Returns:
            上传结果字典
        """
        if not self.is_connected():
            return {
                "success": False,
                "message": "SSH连接未建立"
            }

        try:
            # 检查本地文件
            if not is_file_exists_and_readable(local_path):
                return {
                    "success": False,
                    "message": f"本地文件不存在或不可读: {local_path}"
                }

            # 创建SFTP客户端
            sftp = self.ssh_client.open_sftp()

            # 确保远程目录存在
            remote_dir = os.path.dirname(remote_path)
            if remote_dir:
                try:
                    sftp.makedirs(remote_dir)
                except Exception:
                    pass  # 目录可能已存在

            # 上传文件
            self.logger.info(f"上传文件: {local_path} -> {remote_path}")
            sftp.put(local_path, remote_path)
            sftp.close()

            self.logger.info(f"文件上传成功: {remote_path}")
            return {
                "success": True,
                "message": f"文件上传成功: {os.path.basename(local_path)}"
            }

        except Exception as e:
            error_msg = f"文件上传失败: {str(e)}"
            self.logger.error(error_msg)
            return {
                "success": False,
                "message": error_msg
            }

    def download_file(self, remote_path: str, local_path: str) -> Dict[str, Any]:
        """
        从远程主机下载文件

        Args:
            remote_path: 远程文件路径
            local_path: 本地文件路径

        Returns:
            下载结果字典
        """
        if not self.is_connected():
            return {
                "success": False,
                "message": "SSH连接未建立"
            }

        try:
            # 创建SFTP客户端
            sftp = self.ssh_client.open_sftp()

            # 确保本地目录存在
            local_dir = os.path.dirname(local_path)
            if local_dir:
                os.makedirs(local_dir, exist_ok=True)

            # 下载文件
            self.logger.info(f"下载文件: {remote_path} -> {local_path}")
            sftp.get(remote_path, local_path)
            sftp.close()

            self.logger.info(f"文件下载成功: {local_path}")
            return {
                "success": True,
                "message": f"文件下载成功: {os.path.basename(remote_path)}"
            }

        except Exception as e:
            error_msg = f"文件下载失败: {str(e)}"
            self.logger.error(error_msg)
            return {
                "success": False,
                "message": error_msg
            }

    def close(self) -> None:
        """关闭SSH连接"""
        if self.ssh_client:
            try:
                self.ssh_client.close()
                self.logger.info("SSH连接已关闭")
            except Exception as e:
                self.logger.warning(f"关闭SSH连接时出现异常: {e}")
            finally:
                self.ssh_client = None
                self._connected = False
