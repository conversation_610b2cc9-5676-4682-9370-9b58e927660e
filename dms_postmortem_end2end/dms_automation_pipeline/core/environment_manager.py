"""
环境管理器模块

负责环境相关的管理工作，包括：
- C++文件自动部署
- 配置文件交互确认
- 环境验证
"""

import os
import json
import shutil
import signal
import time
from typing import Dict, Any, List, Tuple, Optional
from pathlib import Path
import threading

try:
    from rich.console import Console
    from rich.prompt import Prompt, Confirm
    from rich.panel import Panel
    from rich.table import Table
    from rich.text import Text
    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False

try:
    from ..utils.logger_config import setup_module_logger
    from ..utils.file_utils import (
        copy_with_permissions, calculate_md5, set_executable_permission,
        ensure_directory_exists, is_file_exists_and_readable, is_file_executable
    )
    from ..utils.validation_utils import validate_path, validate_config_structure
except ImportError:
    import sys
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from utils.logger_config import setup_module_logger
    from utils.file_utils import (
        copy_with_permissions, calculate_md5, set_executable_permission,
        ensure_directory_exists, is_file_exists_and_readable, is_file_executable
    )
    from utils.validation_utils import validate_path, validate_config_structure


class DeploymentResult:
    """部署结果类"""

    def __init__(self, success: bool = False, message: str = "",
                 deployed_files: List[str] = None, failed_files: List[str] = None):
        self.success = success
        self.message = message
        self.deployed_files = deployed_files or []
        self.failed_files = failed_files or []
        self.deployment_time = 0.0

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "success": self.success,
            "message": self.message,
            "deployed_files": self.deployed_files,
            "failed_files": self.failed_files,
            "deployment_time": self.deployment_time
        }


class ConfigConfirmationManager:
    """配置确认管理器"""

    def __init__(self, timeout_seconds: int = 10, default_action: str = "confirm"):
        """
        初始化配置确认管理器

        Args:
            timeout_seconds: 超时时间（秒）
            default_action: 默认动作
        """
        self.timeout_seconds = timeout_seconds
        self.default_action = default_action
        self.logger = setup_module_logger("ConfigConfirmationManager")
        self.console = Console() if RICH_AVAILABLE else None

    def confirm_with_timeout(self, config_file: str, config_content: Dict[str, Any]) -> str:
        """
        带超时的配置确认

        Args:
            config_file: 配置文件路径
            config_content: 配置内容

        Returns:
            确认结果："confirm", "edit", "timeout"
        """
        self.logger.info(f"开始配置确认: {config_file}")

        # 显示配置内容
        self._display_config(config_file, config_content)

        # 设置超时处理
        result = {"action": self.default_action}

        def timeout_handler():
            time.sleep(self.timeout_seconds)
            if result["action"] == self.default_action:
                result["action"] = "timeout"
                if self.console:
                    self.console.print(f"\n[yellow]⏰ 超时({self.timeout_seconds}秒)，使用默认动作: {self.default_action}[/yellow]")
                else:
                    print(f"\n⏰ 超时({self.timeout_seconds}秒)，使用默认动作: {self.default_action}")

        # 启动超时线程
        timeout_thread = threading.Thread(target=timeout_handler, daemon=True)
        timeout_thread.start()

        try:
            if RICH_AVAILABLE and self.console:
                choice = Prompt.ask(
                    f"\n[cyan]请确认配置文件 {os.path.basename(config_file)}[/cyan]",
                    choices=["confirm", "edit", "cancel"],
                    default="confirm"
                )
            else:
                choice = input(f"\n请确认配置文件 {os.path.basename(config_file)} [confirm/edit/cancel, 默认:confirm]: ").strip().lower()
                if not choice:
                    choice = "confirm"

            if choice in ["confirm", "edit", "cancel"]:
                result["action"] = choice

        except (KeyboardInterrupt, EOFError):
            result["action"] = "cancel"

        # 等待超时线程结束
        timeout_thread.join(timeout=0.1)

        final_action = result["action"]
        if final_action == "timeout":
            final_action = self.default_action

        self.logger.info(f"配置确认结果: {final_action}")
        return final_action

    def _display_config(self, config_file: str, config_content: Dict[str, Any]) -> None:
        """显示配置内容"""
        if RICH_AVAILABLE and self.console:
            # 使用Rich显示
            table = Table(title=f"配置文件: {os.path.basename(config_file)}")
            table.add_column("键", style="cyan")
            table.add_column("值", style="green")

            for key, value in config_content.items():
                if isinstance(value, (dict, list)):
                    value_str = json.dumps(value, indent=2, ensure_ascii=False)[:100] + "..."
                else:
                    value_str = str(value)
                table.add_row(key, value_str)

            self.console.print(table)
        else:
            # 简单文本显示
            print(f"\n=== 配置文件: {os.path.basename(config_file)} ===")
            for key, value in config_content.items():
                if isinstance(value, (dict, list)):
                    value_str = json.dumps(value, indent=2, ensure_ascii=False)[:100] + "..."
                else:
                    value_str = str(value)
                print(f"{key}: {value_str}")
            print("=" * 50)


class EnvironmentManager:
    """环境管理器类"""

    def __init__(self, config: Dict[str, Any]):
        """
        初始化环境管理器

        Args:
            config: 配置字典
        """
        self.config = config
        self.logger = setup_module_logger("EnvironmentManager")

        # 获取环境配置
        env_config = config.get("environment", {})
        self.cpp_source_directory = env_config.get("cpp_source_directory", "../BYD_HKH_R_2.01.07.2025.07.08.4_x86/")
        self.runtime_directory = env_config.get("runtime_directory", "./runtime_env/")
        self.required_files = env_config.get("required_files", [])
        self.config_files = env_config.get("config_files", [])

        # 确认配置
        confirmation_config = env_config.get("confirmation", {})
        self.confirmation_manager = ConfigConfirmationManager(
            timeout_seconds=confirmation_config.get("timeout_seconds", 10),
            default_action=confirmation_config.get("default_action", "confirm")
        )

    def validate_source_directory(self) -> Tuple[bool, str]:
        """
        验证源目录

        Returns:
            (是否有效, 错误信息)
        """
        if not validate_path(self.cpp_source_directory, must_exist=True, must_be_dir=True):
            return False, f"C++源目录不存在: {self.cpp_source_directory}"

        # 检查必需文件是否存在
        missing_files = []
        for file_name in self.required_files:
            file_path = os.path.join(self.cpp_source_directory, file_name)
            if not is_file_exists_and_readable(file_path):
                missing_files.append(file_name)

        if missing_files:
            return False, f"源目录中缺少必需文件: {missing_files}"

        return True, ""

    def deploy_cpp_files(self) -> DeploymentResult:
        """
        部署C++文件

        Returns:
            部署结果
        """
        start_time = time.time()
        self.logger.info("开始部署C++文件")

        try:
            # 验证源目录
            valid, error_msg = self.validate_source_directory()
            if not valid:
                return DeploymentResult(
                    success=False,
                    message="源目录验证失败",
                    failed_files=[error_msg]
                )

            # 确保运行时目录存在
            if not ensure_directory_exists(self.runtime_directory):
                return DeploymentResult(
                    success=False,
                    message="无法创建运行时目录",
                    failed_files=[self.runtime_directory]
                )

            deployed_files = []
            failed_files = []

            # 部署必需文件
            for file_name in self.required_files:
                source_path = os.path.join(self.cpp_source_directory, file_name)
                dest_path = os.path.join(self.runtime_directory, file_name)

                try:
                    # 拷贝文件
                    if copy_with_permissions(source_path, dest_path):
                        # 如果是可执行文件，设置可执行权限
                        if not file_name.endswith(('.so', '.ovm', '.json')):
                            set_executable_permission(dest_path)

                        deployed_files.append(file_name)
                        self.logger.debug(f"成功部署文件: {file_name}")
                    else:
                        failed_files.append(f"{file_name}: 拷贝失败")
                        self.logger.error(f"文件拷贝失败: {file_name}")

                except Exception as e:
                    failed_files.append(f"{file_name}: {str(e)}")
                    self.logger.error(f"部署文件异常 {file_name}: {e}")

            # 验证部署结果
            deployment_time = time.time() - start_time

            if failed_files:
                result = DeploymentResult(
                    success=False,
                    message=f"部分文件部署失败: {len(failed_files)}个失败",
                    deployed_files=deployed_files,
                    failed_files=failed_files
                )
            else:
                result = DeploymentResult(
                    success=True,
                    message=f"成功部署所有文件: {len(deployed_files)}个",
                    deployed_files=deployed_files
                )

            result.deployment_time = deployment_time
            self.logger.info(f"C++文件部署完成: 成功{len(deployed_files)}个, 失败{len(failed_files)}个, 耗时{deployment_time:.2f}秒")

            return result

        except Exception as e:
            error_msg = f"C++文件部署异常: {str(e)}"
            self.logger.error(error_msg)
            result = DeploymentResult(
                success=False,
                message="部署过程异常",
                failed_files=[error_msg]
            )
            result.deployment_time = time.time() - start_time
            return result

    def verify_deployment_integrity(self) -> Dict[str, Any]:
        """
        验证部署完整性

        Returns:
            验证结果字典
        """
        self.logger.info("验证部署完整性")

        verification_results = {
            "all_files_present": True,
            "permissions_correct": True,
            "integrity_verified": True,
            "missing_files": [],
            "permission_issues": [],
            "integrity_issues": []
        }

        # 检查文件存在性
        for file_name in self.required_files:
            dest_path = os.path.join(self.runtime_directory, file_name)

            if not is_file_exists_and_readable(dest_path):
                verification_results["all_files_present"] = False
                verification_results["missing_files"].append(file_name)
                continue

            # 检查权限
            if not file_name.endswith(('.so', '.ovm', '.json')):
                if not is_file_executable(dest_path):
                    verification_results["permissions_correct"] = False
                    verification_results["permission_issues"].append(f"{file_name}: 缺少可执行权限")

            # 验证文件完整性（通过大小检查）
            try:
                source_path = os.path.join(self.cpp_source_directory, file_name)
                if os.path.exists(source_path):
                    source_size = os.path.getsize(source_path)
                    dest_size = os.path.getsize(dest_path)

                    if source_size != dest_size:
                        verification_results["integrity_verified"] = False
                        verification_results["integrity_issues"].append(f"{file_name}: 文件大小不匹配")

            except Exception as e:
                verification_results["integrity_verified"] = False
                verification_results["integrity_issues"].append(f"{file_name}: 完整性检查异常 - {str(e)}")

        # 汇总结果
        all_good = (verification_results["all_files_present"] and
                   verification_results["permissions_correct"] and
                   verification_results["integrity_verified"])

        if all_good:
            self.logger.info("部署完整性验证通过")
        else:
            issues = []
            if verification_results["missing_files"]:
                issues.append(f"缺少文件: {verification_results['missing_files']}")
            if verification_results["permission_issues"]:
                issues.append(f"权限问题: {verification_results['permission_issues']}")
            if verification_results["integrity_issues"]:
                issues.append(f"完整性问题: {verification_results['integrity_issues']}")
            self.logger.warning(f"部署完整性验证失败: {'; '.join(issues)}")

        return verification_results

    def load_config_file(self, config_file: str) -> Tuple[bool, Dict[str, Any], str]:
        """
        加载配置文件

        Args:
            config_file: 配置文件路径

        Returns:
            (是否成功, 配置内容, 错误信息)
        """
        try:
            config_path = os.path.join(self.runtime_directory, config_file)

            if not os.path.exists(config_path):
                return False, {}, f"配置文件不存在: {config_file}"

            with open(config_path, 'r', encoding='utf-8') as f:
                config_content = json.load(f)

            return True, config_content, ""

        except json.JSONDecodeError as e:
            return False, {}, f"配置文件JSON格式错误: {e}"
        except Exception as e:
            return False, {}, f"加载配置文件失败: {e}"

    def save_config_file(self, config_file: str, config_content: Dict[str, Any]) -> Tuple[bool, str]:
        """
        保存配置文件

        Args:
            config_file: 配置文件名
            config_content: 配置内容

        Returns:
            (是否成功, 错误信息)
        """
        try:
            config_path = os.path.join(self.runtime_directory, config_file)

            # 备份原文件
            if os.path.exists(config_path):
                backup_path = f"{config_path}.backup"
                shutil.copy2(config_path, backup_path)
                self.logger.debug(f"已备份配置文件: {backup_path}")

            # 保存新配置
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config_content, f, indent=2, ensure_ascii=False)

            self.logger.info(f"配置文件已保存: {config_file}")
            return True, ""

        except Exception as e:
            error_msg = f"保存配置文件失败: {e}"
            self.logger.error(error_msg)
            return False, error_msg

    def validate_config_file(self, config_file: str, config_content: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        验证配置文件

        Args:
            config_file: 配置文件名
            config_content: 配置内容

        Returns:
            (是否有效, 错误列表)
        """
        errors = []

        if config_file == "ip_port.json":
            # 验证ip_port.json
            required_keys = ["ip", "port"]
            if not validate_config_structure(config_content, required_keys):
                errors.append("缺少必需的键: ip, port")

            # 验证IP格式
            ip = config_content.get("ip", "")
            if ip and not self._validate_ip_format(ip):
                errors.append(f"无效的IP地址格式: {ip}")

            # 验证端口
            port = config_content.get("port")
            if port is not None:
                try:
                    port_int = int(port)
                    if not (1 <= port_int <= 65535):
                        errors.append(f"端口号超出范围: {port}")
                except (ValueError, TypeError):
                    errors.append(f"无效的端口号格式: {port}")

        elif config_file == "calidata.json":
            # 验证calidata.json
            if not isinstance(config_content, dict):
                errors.append("配置内容必须是字典格式")
            elif not config_content:
                errors.append("配置内容不能为空")

        return len(errors) == 0, errors

    def _validate_ip_format(self, ip: str) -> bool:
        """验证IP地址格式"""
        try:
            parts = ip.split('.')
            if len(parts) != 4:
                return False
            for part in parts:
                if not (0 <= int(part) <= 255):
                    return False
            return True
        except (ValueError, AttributeError):
            return False

    def confirm_config_file(self, config_file: str) -> str:
        """
        确认配置文件

        Args:
            config_file: 配置文件名

        Returns:
            确认结果："confirm", "edit", "timeout", "error"
        """
        self.logger.info(f"开始配置文件确认: {config_file}")

        try:
            # 加载配置文件
            success, config_content, error_msg = self.load_config_file(config_file)
            if not success:
                self.logger.error(f"无法加载配置文件 {config_file}: {error_msg}")
                return "error"

            # 验证配置文件
            valid, errors = self.validate_config_file(config_file, config_content)
            if not valid:
                self.logger.warning(f"配置文件 {config_file} 验证失败: {errors}")
                if RICH_AVAILABLE:
                    console = Console()
                    console.print(f"[red]配置文件验证失败:[/red]")
                    for error in errors:
                        console.print(f"  - {error}")
                else:
                    print(f"配置文件验证失败:")
                    for error in errors:
                        print(f"  - {error}")

            # 使用确认管理器进行确认
            result = self.confirmation_manager.confirm_with_timeout(config_file, config_content)

            return result

        except Exception as e:
            error_msg = f"配置文件确认异常: {e}"
            self.logger.error(error_msg)
            return "error"

    def edit_config_interactive(self, config_file: str) -> Dict[str, Any]:
        """
        交互式编辑配置

        Args:
            config_file: 配置文件名

        Returns:
            编辑结果字典
        """
        self.logger.info(f"开始交互式编辑配置: {config_file}")

        try:
            # 加载当前配置
            success, config_content, error_msg = self.load_config_file(config_file)
            if not success:
                return {
                    "success": False,
                    "message": f"无法加载配置文件: {error_msg}"
                }

            # 简化的编辑界面
            if config_file == "ip_port.json":
                result = self._edit_ip_port_config(config_content)
            elif config_file == "calidata.json":
                result = self._edit_calidata_config(config_content)
            else:
                return {
                    "success": False,
                    "message": f"不支持编辑的配置文件类型: {config_file}"
                }

            if result["success"]:
                # 保存编辑后的配置
                save_success, save_error = self.save_config_file(config_file, result["config"])
                if not save_success:
                    return {
                        "success": False,
                        "message": f"保存配置失败: {save_error}"
                    }

                self.logger.info(f"配置文件编辑完成: {config_file}")

            return result

        except Exception as e:
            error_msg = f"交互式编辑异常: {e}"
            self.logger.error(error_msg)
            return {
                "success": False,
                "message": error_msg
            }

    def _edit_ip_port_config(self, config_content: Dict[str, Any]) -> Dict[str, Any]:
        """编辑IP端口配置"""
        try:
            current_ip = config_content.get("ip", "***********")
            current_port = config_content.get("port", 1180)

            if RICH_AVAILABLE:
                console = Console()
                console.print(f"[cyan]当前配置:[/cyan] IP={current_ip}, Port={current_port}")

                new_ip = Prompt.ask("请输入新的IP地址", default=str(current_ip))
                new_port = Prompt.ask("请输入新的端口号", default=str(current_port))
            else:
                print(f"当前配置: IP={current_ip}, Port={current_port}")
                new_ip = input(f"请输入新的IP地址 (默认: {current_ip}): ").strip() or str(current_ip)
                new_port = input(f"请输入新的端口号 (默认: {current_port}): ").strip() or str(current_port)

            # 验证输入
            if not self._validate_ip_format(new_ip):
                return {
                    "success": False,
                    "message": f"无效的IP地址格式: {new_ip}"
                }

            try:
                port_int = int(new_port)
                if not (1 <= port_int <= 65535):
                    return {
                        "success": False,
                        "message": f"端口号超出范围: {new_port}"
                    }
            except ValueError:
                return {
                    "success": False,
                    "message": f"无效的端口号格式: {new_port}"
                }

            # 更新配置
            new_config = {
                "ip": new_ip,
                "port": port_int
            }

            return {
                "success": True,
                "message": "IP端口配置编辑完成",
                "config": new_config
            }

        except (KeyboardInterrupt, EOFError):
            return {
                "success": False,
                "message": "用户取消编辑"
            }
        except Exception as e:
            return {
                "success": False,
                "message": f"编辑IP端口配置异常: {e}"
            }

    def _edit_calidata_config(self, config_content: Dict[str, Any]) -> Dict[str, Any]:
        """编辑校准数据配置"""
        try:
            if RICH_AVAILABLE:
                console = Console()
                console.print("[yellow]注意: calidata.json配置较为复杂，建议手动编辑[/yellow]")

                edit_choice = Confirm.ask("是否继续简单编辑模式？")
                if not edit_choice:
                    return {
                        "success": False,
                        "message": "用户选择不编辑"
                    }
            else:
                print("注意: calidata.json配置较为复杂，建议手动编辑")
                choice = input("是否继续简单编辑模式？ [y/N]: ").strip().lower()
                if choice not in ['y', 'yes']:
                    return {
                        "success": False,
                        "message": "用户选择不编辑"
                    }

            # 简单的键值编辑
            new_config = config_content.copy()

            if RICH_AVAILABLE:
                console.print("[cyan]当前配置键:[/cyan]")
                for key in config_content.keys():
                    console.print(f"  - {key}")

                key_to_edit = Prompt.ask("请输入要编辑的键名 (留空跳过)")
                if key_to_edit and key_to_edit in config_content:
                    current_value = config_content[key_to_edit]
                    new_value = Prompt.ask(f"请输入新值 (当前: {current_value})")

                    # 尝试解析为适当的类型
                    try:
                        if isinstance(current_value, (int, float)):
                            new_config[key_to_edit] = type(current_value)(new_value)
                        elif isinstance(current_value, bool):
                            new_config[key_to_edit] = new_value.lower() in ['true', '1', 'yes']
                        else:
                            new_config[key_to_edit] = new_value
                    except ValueError:
                        new_config[key_to_edit] = new_value
            else:
                print("当前配置键:")
                for key in config_content.keys():
                    print(f"  - {key}")

                key_to_edit = input("请输入要编辑的键名 (留空跳过): ").strip()
                if key_to_edit and key_to_edit in config_content:
                    current_value = config_content[key_to_edit]
                    new_value = input(f"请输入新值 (当前: {current_value}): ").strip()

                    if new_value:
                        try:
                            if isinstance(current_value, (int, float)):
                                new_config[key_to_edit] = type(current_value)(new_value)
                            elif isinstance(current_value, bool):
                                new_config[key_to_edit] = new_value.lower() in ['true', '1', 'yes']
                            else:
                                new_config[key_to_edit] = new_value
                        except ValueError:
                            new_config[key_to_edit] = new_value

            return {
                "success": True,
                "message": "校准数据配置编辑完成",
                "config": new_config
            }

        except (KeyboardInterrupt, EOFError):
            return {
                "success": False,
                "message": "用户取消编辑"
            }
        except Exception as e:
            return {
                "success": False,
                "message": f"编辑校准数据配置异常: {e}"
            }

    def validate_environment(self) -> Dict[str, Any]:
        """
        验证环境

        Returns:
            验证结果字典
        """
        self.logger.info("开始环境验证")

        validation_result = {
            "all_files_exist": True,
            "permissions_correct": True,
            "configs_valid": True,
            "missing_files": [],
            "permission_issues": [],
            "config_issues": [],
            "summary": ""
        }

        try:
            # 1. 验证运行时目录
            if not os.path.exists(self.runtime_directory):
                validation_result["all_files_exist"] = False
                validation_result["missing_files"].append(f"运行时目录: {self.runtime_directory}")

            # 2. 验证必需文件
            for file_name in self.required_files:
                file_path = os.path.join(self.runtime_directory, file_name)

                # 检查文件存在性
                if not is_file_exists_and_readable(file_path):
                    validation_result["all_files_exist"] = False
                    validation_result["missing_files"].append(file_name)
                    continue

                # 检查可执行权限（对于非库文件）
                if not file_name.endswith(('.so', '.ovm', '.json')):
                    if not is_file_executable(file_path):
                        validation_result["permissions_correct"] = False
                        validation_result["permission_issues"].append(f"{file_name}: 缺少可执行权限")

            # 3. 验证配置文件
            for config_file in self.config_files:
                config_path = os.path.join(self.runtime_directory, config_file)

                if not os.path.exists(config_path):
                    validation_result["configs_valid"] = False
                    validation_result["config_issues"].append(f"{config_file}: 文件不存在")
                    continue

                # 验证配置文件内容
                success, config_content, error_msg = self.load_config_file(config_file)
                if not success:
                    validation_result["configs_valid"] = False
                    validation_result["config_issues"].append(f"{config_file}: {error_msg}")
                    continue

                # 验证配置文件格式
                valid, errors = self.validate_config_file(config_file, config_content)
                if not valid:
                    validation_result["configs_valid"] = False
                    for error in errors:
                        validation_result["config_issues"].append(f"{config_file}: {error}")

            # 4. 生成验证摘要
            issues = []
            if not validation_result["all_files_exist"]:
                issues.append(f"缺少文件: {len(validation_result['missing_files'])}个")
            if not validation_result["permissions_correct"]:
                issues.append(f"权限问题: {len(validation_result['permission_issues'])}个")
            if not validation_result["configs_valid"]:
                issues.append(f"配置问题: {len(validation_result['config_issues'])}个")

            if issues:
                validation_result["summary"] = f"环境验证失败: {'; '.join(issues)}"
                self.logger.warning(validation_result["summary"])
            else:
                validation_result["summary"] = "环境验证通过"
                self.logger.info("环境验证通过")

            return validation_result

        except Exception as e:
            error_msg = f"环境验证异常: {e}"
            self.logger.error(error_msg)
            validation_result.update({
                "all_files_exist": False,
                "permissions_correct": False,
                "configs_valid": False,
                "summary": error_msg
            })
            return validation_result

    def get_environment_status(self) -> Dict[str, Any]:
        """
        获取环境状态信息

        Returns:
            环境状态字典
        """
        status = {
            "cpp_source_directory": self.cpp_source_directory,
            "runtime_directory": self.runtime_directory,
            "required_files": self.required_files,
            "config_files": self.config_files,
            "source_directory_exists": os.path.exists(self.cpp_source_directory),
            "runtime_directory_exists": os.path.exists(self.runtime_directory),
            "deployed_files": [],
            "missing_files": [],
            "config_status": {}
        }

        # 检查已部署的文件
        for file_name in self.required_files:
            file_path = os.path.join(self.runtime_directory, file_name)
            if os.path.exists(file_path):
                status["deployed_files"].append({
                    "name": file_name,
                    "size": os.path.getsize(file_path),
                    "executable": is_file_executable(file_path)
                })
            else:
                status["missing_files"].append(file_name)

        # 检查配置文件状态
        for config_file in self.config_files:
            config_path = os.path.join(self.runtime_directory, config_file)
            if os.path.exists(config_path):
                success, config_content, error_msg = self.load_config_file(config_file)
                valid, errors = self.validate_config_file(config_file, config_content) if success else (False, [error_msg])

                status["config_status"][config_file] = {
                    "exists": True,
                    "valid": valid,
                    "errors": errors if not valid else [],
                    "size": os.path.getsize(config_path)
                }
            else:
                status["config_status"][config_file] = {
                    "exists": False,
                    "valid": False,
                    "errors": ["文件不存在"],
                    "size": 0
                }

        return status
