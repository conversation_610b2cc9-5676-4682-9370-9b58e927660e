#!/usr/bin/env python3
"""
DMS自动化流水线主入口程序

提供命令行接口，支持：
- 配置文件加载和验证
- 干运行模式
- 完整流水线执行
- 断点续传
- 进度显示
"""

import sys
import os
import argparse
from typing import Dict, Any, Optional

# 添加当前目录到Python路径以支持包导入
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

try:
    from dms_automation_pipeline.core.task_orchestrator import TaskOrchestrator, StageResult
    from dms_automation_pipeline.config.config_manager import ConfigManager, ConfigValidationError
    from dms_automation_pipeline.utils.logger_config import setup_logger, configure_rich_logging
    from dms_automation_pipeline.utils.validation_utils import validate_path
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在正确的目录中运行此程序")
    sys.exit(1)

try:
    from rich.console import Console
    from rich.panel import Panel
    from rich.text import Text
    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False


class DMSAutomationApp:
    """DMS自动化应用主类"""
    
    def __init__(self):
        """初始化应用"""
        self.console = Console() if RICH_AVAILABLE else None
        self.logger = None
        self.config_manager = ConfigManager()
        self.orchestrator = None
        
    def setup_logging(self, config: Dict[str, Any]) -> None:
        """设置日志"""
        logging_config = config.get("logging", {})
        
        self.logger = setup_logger(
            name="dms_automation",
            log_level=logging_config.get("level", "INFO"),
            log_file=logging_config.get("log_file") if logging_config.get("file_output", True) else None,
            console_output=logging_config.get("console_output", True)
        )
        
        # 配置Rich日志（如果可用且启用）
        if RICH_AVAILABLE and logging_config.get("rich_formatting", True):
            configure_rich_logging()
            
        self.logger.info("DMS自动化流水线启动")
        
    def print_banner(self) -> None:
        """打印程序横幅"""
        if RICH_AVAILABLE and self.console:
            banner_text = Text()
            banner_text.append("DMS自动化流水线", style="bold blue")
            banner_text.append("\n端到端DMS分析自动化系统", style="italic")
            banner_text.append("\nv1.0.0", style="dim")
            
            panel = Panel(
                banner_text,
                title="🚀 欢迎使用",
                border_style="blue",
                padding=(1, 2)
            )
            self.console.print(panel)
        else:
            print("=" * 50)
            print("    DMS自动化流水线")
            print("    端到端DMS分析自动化系统")
            print("    v1.0.0")
            print("=" * 50)
            
    def validate_environment(self, config: Dict[str, Any]) -> bool:
        """验证运行环境"""
        if self.logger:
            self.logger.info("验证运行环境...")
            
        # 检查配置中的路径
        missing_paths = self.config_manager.validate_paths_exist(config)
        if missing_paths:
            if self.logger:
                self.logger.warning(f"发现缺失路径: {missing_paths}")
            if RICH_AVAILABLE and self.console:
                self.console.print(f"[yellow]警告: 发现缺失路径[/yellow]")
                for path in missing_paths:
                    self.console.print(f"  - {path}")
            else:
                print(f"警告: 发现缺失路径: {missing_paths}")
                
        return True
        
    def run_dry_run(self, config: Dict[str, Any]) -> bool:
        """执行干运行"""
        if self.logger:
            self.logger.info("执行干运行模式")
            
        if RICH_AVAILABLE and self.console:
            self.console.print("[cyan]🔍 干运行模式 - 仅验证配置，不执行实际操作[/cyan]")
        else:
            print("🔍 干运行模式 - 仅验证配置，不执行实际操作")
            
        # 验证配置
        try:
            self.config_manager.validate_config(config)
            if RICH_AVAILABLE and self.console:
                self.console.print("[green]✓ 配置验证通过[/green]")
            else:
                print("✓ 配置验证通过")
        except ConfigValidationError as e:
            if RICH_AVAILABLE and self.console:
                self.console.print(f"[red]❌ 配置验证失败: {e}[/red]")
            else:
                print(f"❌ 配置验证失败: {e}")
            return False
            
        # 验证环境
        if not self.validate_environment(config):
            return False
            
        # 显示将要执行的阶段
        stages = config.get("pipeline", {}).get("stages", [
            "VIDEO_PROCESSING", "ENV_SETUP", "REMOTE_VALIDATION", "RENDERING"
        ])
        
        if RICH_AVAILABLE and self.console:
            self.console.print("\n[cyan]将要执行的阶段:[/cyan]")
            for i, stage in enumerate(stages, 1):
                self.console.print(f"  {i}. {stage}")
        else:
            print("\n将要执行的阶段:")
            for i, stage in enumerate(stages, 1):
                print(f"  {i}. {stage}")
                
        if self.logger:
            self.logger.info("干运行完成")
            
        return True
        
    def run_full_pipeline(self, config: Dict[str, Any]) -> bool:
        """执行完整流水线"""
        if self.logger:
            self.logger.info("开始执行完整流水线")
            
        # 创建任务编排器
        self.orchestrator = TaskOrchestrator(config)
        
        # 显示当前状态
        self.orchestrator.display_status()
        
        # 执行流水线
        result = self.orchestrator.execute_full_pipeline()
        
        if result.success:
            if RICH_AVAILABLE and self.console:
                self.console.print("[green]🎉 流水线执行成功![/green]")
            else:
                print("🎉 流水线执行成功!")
            if self.logger:
                self.logger.info("流水线执行成功")
            return True
        else:
            if RICH_AVAILABLE and self.console:
                self.console.print(f"[red]❌ 流水线执行失败: {result.error or result.message}[/red]")
            else:
                print(f"❌ 流水线执行失败: {result.error or result.message}")
            if self.logger:
                self.logger.error(f"流水线执行失败: {result.error or result.message}")
            return False
            
    def run_validate_only(self, config: Dict[str, Any]) -> bool:
        """仅验证配置"""
        if self.logger:
            self.logger.info("仅验证配置模式")
            
        try:
            # 验证配置结构
            self.config_manager.validate_config(config)
            
            # 验证环境
            self.validate_environment(config)
            
            if RICH_AVAILABLE and self.console:
                self.console.print("[green]✓ 所有验证通过[/green]")
            else:
                print("✓ 所有验证通过")
                
            return True
            
        except ConfigValidationError as e:
            if RICH_AVAILABLE and self.console:
                self.console.print(f"[red]❌ 验证失败: {e}[/red]")
            else:
                print(f"❌ 验证失败: {e}")
            return False
            
    def main(self, args: argparse.Namespace) -> int:
        """主执行函数"""
        try:
            # 打印横幅
            self.print_banner()
            
            # 加载配置
            if not os.path.exists(args.config):
                print(f"❌ 配置文件不存在: {args.config}")
                return 1
                
            config = self.config_manager.load_and_validate_config(args.config)
            
            # 设置日志
            self.setup_logging(config)
            
            # 根据参数执行不同操作
            if args.validate_only:
                success = self.run_validate_only(config)
            elif args.dry_run:
                success = self.run_dry_run(config)
            else:
                success = self.run_full_pipeline(config)
                
            return 0 if success else 1
            
        except ConfigValidationError as e:
            print(f"❌ 配置错误: {e}")
            return 1
        except KeyboardInterrupt:
            print("\n⚠️ 用户中断执行")
            if self.logger:
                self.logger.warning("用户中断执行")
            return 130
        except Exception as e:
            print(f"❌ 程序异常: {e}")
            if self.logger:
                self.logger.error(f"程序异常: {e}")
            return 1


def create_argument_parser() -> argparse.ArgumentParser:
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description="DMS自动化流水线 - 端到端DMS分析自动化系统",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  %(prog)s --config config.json                    # 执行完整流水线
  %(prog)s --config config.json --dry-run          # 干运行模式
  %(prog)s --config config.json --validate-only    # 仅验证配置
  %(prog)s --help                                  # 显示帮助信息
        """
    )
    
    parser.add_argument(
        "--config", "-c",
        required=True,
        help="配置文件路径 (JSON格式)"
    )
    
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="干运行模式，仅验证配置不执行实际操作"
    )
    
    parser.add_argument(
        "--validate-only",
        action="store_true", 
        help="仅验证配置文件和环境，不执行流水线"
    )
    
    parser.add_argument(
        "--version",
        action="version",
        version="DMS自动化流水线 v1.0.0"
    )
    
    return parser


def main() -> int:
    """程序入口点"""
    parser = create_argument_parser()
    args = parser.parse_args()
    
    app = DMSAutomationApp()
    return app.main(args)


if __name__ == "__main__":
    sys.exit(main())
