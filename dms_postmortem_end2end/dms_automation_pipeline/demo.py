#!/usr/bin/env python3
"""
DMS自动化分析与渲染平台 - 演示脚本

展示平台的核心功能和工作流程
"""

import os
import sys
import time
import tempfile
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from core.task_orchestrator import TaskOrchestrator, PipelineState
    from core.config_manager import ConfigManager
except ImportError:
    # 如果相对导入失败，尝试直接导入
    import importlib.util

    # 直接加载TaskOrchestrator
    spec = importlib.util.spec_from_file_location(
        "task_orchestrator",
        os.path.join(os.path.dirname(__file__), "core", "task_orchestrator.py")
    )
    task_orchestrator_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(task_orchestrator_module)
    TaskOrchestrator = task_orchestrator_module.TaskOrchestrator
    PipelineState = task_orchestrator_module.PipelineState

    # 直接加载ConfigManager
    spec = importlib.util.spec_from_file_location(
        "config_manager",
        os.path.join(os.path.dirname(__file__), "core", "config_manager.py")
    )
    config_manager_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(config_manager_module)
    ConfigManager = config_manager_module.ConfigManager


def create_demo_environment():
    """创建演示环境"""
    print("🎬 创建演示环境...")
    
    # 创建演示视频文件
    input_dir = Path("input_videos")
    input_dir.mkdir(exist_ok=True)
    
    demo_videos = []
    for i in range(3):
        video_path = input_dir / f"demo_video_{i+1}.mp4"
        with open(video_path, 'w') as f:
            f.write(f"# 演示视频文件 {i+1}\n")
            f.write(f"# 这是一个模拟的视频文件，用于演示DMS自动化平台\n")
            f.write(f"# 实际使用时，这里应该是真实的MP4视频文件\n")
            f.write(f"# 文件大小: 模拟 {(i+1)*10}MB\n")
            f.write(f"# 时长: 模拟 {(i+1)*30}秒\n")
        demo_videos.append(str(video_path))
    
    print(f"✅ 创建了 {len(demo_videos)} 个演示视频文件")
    return demo_videos


def create_demo_config():
    """创建演示配置"""
    print("⚙️ 创建演示配置...")
    
    demo_config = {
        "video_processing": {
            "enabled": True,
            "input_directory": "./input_videos/",
            "output_directory": "./cropped_videos/",
            "max_workers": 2,
            "force_reprocess": True,  # 演示模式强制重新处理
            "duplicate_detection": {
                "enabled": True,
                "strategy": "filename_based"
            }
        },
        "environment": {
            "enabled": True,
            "cpp_source_directory": "./demo_cpp/",  # 演示用的C++目录
            "runtime_directory": "./runtime_env/",
            "required_files": [
                "demo_dms_program",
                "demo_lib.so",
                "demo_model.ovm"
            ],
            "config_files": ["ip_port.json", "calidata.json"],
            "confirmation": {
                "timeout_seconds": 3,  # 演示用短超时
                "default_action": "confirm"
            }
        },
        "remote": {
            "enabled": False,  # 演示模式禁用远程功能
            "ssh": {
                "host": "demo.example.com",
                "port": 22,
                "username": "demo_user"
            }
        },
        "rendering": {
            "enabled": True,
            "max_concurrent_tasks": 2,
            "task_timeout": 30,  # 演示用短超时
            "output_directory": "./output/",
            "batch_size": 5,
            "min_success_rate": 0.8,
            "results": {
                "directory": "./results/",
                "generate_reports": True,
                "verify_outputs": True,
                "collect_statistics": True
            }
        },
        "logging": {
            "level": "INFO",
            "file": "./logs/demo.log"
        }
    }
    
    # 创建演示用的C++文件
    cpp_dir = Path("demo_cpp")
    cpp_dir.mkdir(exist_ok=True)
    
    demo_files = demo_config["environment"]["required_files"]
    for file_name in demo_files:
        file_path = cpp_dir / file_name
        with open(file_path, 'w') as f:
            f.write(f"# 演示用的 {file_name}\n")
            f.write(f"# 这是一个模拟的文件，用于演示环境管理功能\n")
        
        # 设置可执行权限（对于程序文件）
        if not file_name.endswith(('.so', '.ovm')):
            os.chmod(file_path, 0o755)
    
    # 创建演示配置文件
    runtime_dir = Path("runtime_env")
    runtime_dir.mkdir(exist_ok=True)
    
    # 创建ip_port.json
    ip_port_config = {"ip": "127.0.0.1", "port": 8080}
    with open(runtime_dir / "ip_port.json", 'w') as f:
        import json
        json.dump(ip_port_config, f, indent=2)
    
    # 创建calidata.json
    calidata_config = {
        "camera_matrix": [[1000, 0, 320], [0, 1000, 240], [0, 0, 1]],
        "distortion": [0.1, -0.2, 0.0, 0.0, 0.0],
        "demo_mode": True
    }
    with open(runtime_dir / "calidata.json", 'w') as f:
        json.dump(calidata_config, f, indent=2)
    
    print("✅ 演示配置创建完成")
    return demo_config


def run_demo_workflow():
    """运行演示工作流"""
    print("\n🚀 开始DMS自动化分析与渲染平台演示")
    print("=" * 60)
    
    # 创建演示环境
    demo_videos = create_demo_environment()
    demo_config = create_demo_config()
    
    # 初始化任务编排器
    print("\n🎯 初始化任务编排器...")
    orchestrator = TaskOrchestrator(demo_config)
    
    print(f"✅ 当前状态: {orchestrator.current_state.value}")
    print(f"✅ 注册的执行器: {list(orchestrator.stage_executors.keys())}")
    
    # 执行工作流
    stages_to_demo = [
        ("VIDEO_PROCESSING", "🎬 视频预处理阶段"),
        ("ENV_SETUP", "🔧 环境管理阶段"),
        ("RENDERING", "🎨 渲染执行阶段")
    ]
    
    for stage_name, stage_desc in stages_to_demo:
        print(f"\n{stage_desc}")
        print("-" * 40)
        
        try:
            # 检查是否有对应的执行器
            if stage_name not in orchestrator.stage_executors:
                print(f"⚠️  跳过 {stage_name}：执行器未注册")
                continue
            
            print(f"⏳ 执行阶段: {stage_name}")
            start_time = time.time()
            
            # 执行阶段
            result = orchestrator.execute_stage(stage_name)
            
            execution_time = time.time() - start_time
            
            if result.success:
                print(f"✅ {stage_desc} 完成")
                print(f"📝 消息: {result.message}")
                print(f"⏱️  执行时间: {execution_time:.2f}秒")
                
                if result.data:
                    # 显示关键数据
                    if "processed_videos" in result.data:
                        print(f"📊 处理视频数: {result.data['processed_videos']}")
                    if "deployed_files_count" in result.data:
                        print(f"📊 部署文件数: {result.data['deployed_files_count']}")
                    if "video_files_processed" in result.data:
                        print(f"📊 渲染视频数: {result.data['video_files_processed']}")
                    if "success_rate" in result.data:
                        print(f"📊 成功率: {result.data['success_rate']:.1%}")
            else:
                print(f"❌ {stage_desc} 失败")
                print(f"📝 错误: {result.error or result.message}")
                
                # 演示模式下继续执行其他阶段
                print("🔄 演示模式：继续执行下一阶段")
            
            print(f"🎯 当前状态: {orchestrator.current_state.value}")
            
        except Exception as e:
            print(f"❌ 执行 {stage_name} 时发生异常: {e}")
            print("🔄 演示模式：继续执行下一阶段")
    
    # 显示最终状态
    print(f"\n🏁 演示完成")
    print(f"🎯 最终状态: {orchestrator.current_state.value}")
    
    # 显示生成的文件
    print(f"\n📁 生成的文件和目录:")
    directories_to_check = [
        "cropped_videos", "runtime_env", "output", "results", "logs"
    ]
    
    for dir_name in directories_to_check:
        dir_path = Path(dir_name)
        if dir_path.exists():
            files = list(dir_path.iterdir())
            print(f"📂 {dir_name}/: {len(files)} 个文件")
            for file_path in files[:3]:  # 只显示前3个文件
                print(f"   📄 {file_path.name}")
            if len(files) > 3:
                print(f"   ... 还有 {len(files) - 3} 个文件")
        else:
            print(f"📂 {dir_name}/: 目录不存在")


def cleanup_demo():
    """清理演示文件"""
    print("\n🧹 清理演示文件...")
    
    cleanup_dirs = [
        "input_videos", "cropped_videos", "demo_cpp", 
        "runtime_env", "output", "results", "logs", "temp"
    ]
    
    import shutil
    
    for dir_name in cleanup_dirs:
        dir_path = Path(dir_name)
        if dir_path.exists():
            try:
                shutil.rmtree(dir_path)
                print(f"✅ 已清理: {dir_name}/")
            except Exception as e:
                print(f"⚠️  清理失败 {dir_name}/: {e}")
    
    print("✅ 演示文件清理完成")


def main():
    """主函数"""
    print("🎯 DMS自动化分析与渲染平台 - 功能演示")
    print("=" * 60)
    print("这是一个完整的功能演示，将展示平台的核心工作流程")
    print("注意：这是演示模式，使用模拟数据，不会执行实际的视频处理")
    print("=" * 60)
    
    try:
        # 询问用户是否继续
        response = input("\n是否开始演示？[Y/n]: ").strip().lower()
        if response and response not in ['y', 'yes', '是']:
            print("演示已取消")
            return
        
        # 运行演示
        run_demo_workflow()
        
        # 询问是否清理
        print("\n" + "=" * 60)
        response = input("是否清理演示文件？[Y/n]: ").strip().lower()
        if not response or response in ['y', 'yes', '是']:
            cleanup_demo()
        else:
            print("演示文件已保留，您可以手动检查生成的文件")
        
        print("\n🎉 演示完成！感谢使用DMS自动化分析与渲染平台")
        
    except KeyboardInterrupt:
        print("\n\n⏹️  演示被用户中断")
    except Exception as e:
        print(f"\n❌ 演示过程中发生异常: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
