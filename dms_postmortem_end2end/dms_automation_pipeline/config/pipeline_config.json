{"video_processing": {"enabled": true, "input_video_file": "/home/<USER>/data/mount/byd/dms/元UP-SC3E_R视频放在这里/SC3E-R_20250714跟测视频/2025-07-14 14-22-29.mp4", "time_range": {"start": "00:02:00", "end": "00:02:30"}, "output_directory": "./cropped_videos/", "max_workers": 2, "force_reprocess": true, "duplicate_detection": {"enabled": false, "strategy": "filename_based"}}, "environment": {"enabled": true, "cpp_source_directory": "../BYD_HKH_R_2.01.07.2025.07.08.4_x86/", "runtime_directory": "./runtime_env/", "required_files": ["test_dms_internal_postmortem", "libtx_dms.so", "FaceDetection.ovm", "FaceKeypoints.ovm", "eye.ovm"], "config_files": ["ip_port.json", "calidata.json"], "confirmation": {"timeout_seconds": 10, "default_action": "confirm"}}, "remote": {"enabled": true, "ssh": {"host": "***********", "port": 22, "username": "user", "key_file": "~/.ssh/id_rsa", "timeout": 30, "retry_count": 3}, "service": {"path": "/userfs/tx_dms_oax_test_tool_update", "port": 1180, "start_command": "cd /userfs && ./tx_dms_oax_test_tool_update", "max_wait_time": 60}, "model_sync": {"enabled": true, "local_model_path": "./runtime_env/", "remote_model_path": "/userfs/models/", "model_files": ["FaceDetection.ovm", "FaceKeypoints.ovm", "eye.ovm"], "verify_md5": true}}, "rendering": {"enabled": true, "max_concurrent_tasks": 2, "task_timeout": 3600, "output_directory": "./output/", "batch_size": 10, "min_success_rate": 0.8, "results": {"directory": "./results/", "generate_reports": true, "verify_outputs": true, "collect_statistics": true}}, "logging": {"level": "INFO", "file": "./logs/pipeline.log", "max_size": "10MB", "backup_count": 5}}