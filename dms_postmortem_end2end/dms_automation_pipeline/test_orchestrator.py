#!/usr/bin/env python3
"""
TaskOrchestrator测试脚本

用于验证TaskOrchestrator的基本功能
"""

import sys
import os

# 添加父目录到Python路径以支持包导入
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

# 现在可以正确导入
from dms_automation_pipeline.core.task_orchestrator import TaskOrchestrator, StageResult


def test_basic_functionality():
    """测试基本功能"""
    print("=== 测试TaskOrchestrator基本功能 ===")
    
    # 创建配置
    config = {
        "test_mode": True,
        "video_processing": {"enabled": True},
        "environment": {"enabled": True}
    }
    
    # 测试初始化
    orchestrator = TaskOrchestrator(config)
    print(f"✓ 初始化成功，当前状态: {orchestrator.current_state.value}")
    assert orchestrator.current_state.value == "INIT"
    
    # 测试状态设置
    orchestrator.set_state("VIDEO_PROCESSING")
    print(f"✓ 状态设置成功: {orchestrator.current_state.value}")
    assert orchestrator.current_state.value == "VIDEO_PROCESSING"
    
    # 测试状态保存和加载
    orchestrator.save_state()
    print("✓ 状态保存成功")
    
    # 测试从状态文件加载
    new_orchestrator = TaskOrchestrator.load_from_state(config)
    print(f"✓ 状态加载成功: {new_orchestrator.current_state.value}")
    assert new_orchestrator.current_state.value == "VIDEO_PROCESSING"
    
    return True


def test_stage_execution():
    """测试阶段执行"""
    print("\n=== 测试阶段执行功能 ===")
    
    config = {"test_mode": True}
    orchestrator = TaskOrchestrator(config)
    
    # 测试执行阶段
    result = orchestrator.execute_stage("VIDEO_PROCESSING")
    print(f"✓ 阶段执行成功: {result.success}")
    assert result.success == True
    assert orchestrator.current_state.value == "ENV_SETUP"
    
    # 测试获取阶段结果
    stage_result = orchestrator.get_stage_result("VIDEO_PROCESSING")
    print(f"✓ 获取阶段结果: {stage_result.success}")
    assert stage_result.success == True
    
    return True


def test_stage_result():
    """测试StageResult类"""
    print("\n=== 测试StageResult类 ===")
    
    # 创建结果对象
    result = StageResult(
        success=True,
        message="测试成功",
        data={"processed_files": 5},
        error=None
    )
    
    # 测试转换为字典
    result_dict = result.to_dict()
    print(f"✓ 转换为字典: {result_dict['success']}")
    assert result_dict["success"] == True
    assert result_dict["message"] == "测试成功"
    assert result_dict["data"]["processed_files"] == 5
    
    # 测试从字典创建
    new_result = StageResult.from_dict(result_dict)
    print(f"✓ 从字典创建: {new_result.success}")
    assert new_result.success == True
    assert new_result.message == "测试成功"
    assert new_result.data["processed_files"] == 5
    
    return True


def test_progress_display():
    """测试进度显示（如果Rich可用）"""
    print("\n=== 测试进度显示功能 ===")
    
    config = {"test_mode": True}
    orchestrator = TaskOrchestrator(config)
    
    # 测试进度显示
    orchestrator.start_progress_display("测试进度")
    orchestrator.update_progress(25, "第一步完成")
    orchestrator.update_progress(25, "第二步完成")
    orchestrator.update_progress(25, "第三步完成")
    orchestrator.update_progress(25, "全部完成")
    orchestrator.stop_progress_display()
    
    print("✓ 进度显示测试完成")
    return True


def main():
    """主测试函数"""
    try:
        print("开始测试TaskOrchestrator...")
        
        # 运行所有测试
        tests = [
            test_basic_functionality,
            test_stage_execution,
            test_stage_result,
            test_progress_display
        ]
        
        for test in tests:
            if not test():
                print(f"❌ 测试失败: {test.__name__}")
                return False
                
        print("\n🎉 所有测试通过！")
        
        # 清理测试文件
        if os.path.exists(".pipeline_state.json"):
            os.remove(".pipeline_state.json")
            print("✓ 清理测试文件完成")
            
        return True
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
