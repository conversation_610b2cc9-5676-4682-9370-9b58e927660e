#!/usr/bin/env python3
"""
环境管理测试脚本

验证EnvironmentManager和EnvironmentExecutor的功能
"""

import sys
import os
import tempfile
import json
from unittest.mock import patch, MagicMock

# 添加父目录到Python路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

from dms_automation_pipeline.core.environment_manager import EnvironmentManager, ConfigConfirmationManager
from dms_automation_pipeline.core.environment_executor import EnvironmentExecutor
from dms_automation_pipeline.core.task_orchestrator import TaskOrchestrator


def create_test_environment_config(temp_dir: str) -> dict:
    """创建测试环境配置"""
    source_dir = os.path.join(temp_dir, "cpp_source")
    runtime_dir = os.path.join(temp_dir, "runtime")
    
    # 创建目录
    os.makedirs(source_dir, exist_ok=True)
    os.makedirs(runtime_dir, exist_ok=True)
    
    # 创建测试文件
    test_files = [
        "test_dms_internal_postmortem",
        "libtx_dms.so",
        "FaceDetection.ovm",
        "FaceKeypoints.ovm",
        "eye.ovm"
    ]
    
    for file_name in test_files:
        file_path = os.path.join(source_dir, file_name)
        with open(file_path, 'w') as f:
            f.write(f"fake {file_name} content")
        # 设置可执行权限（对于非库文件）
        if not file_name.endswith(('.so', '.ovm')):
            os.chmod(file_path, 0o755)
    
    # 创建配置文件
    ip_port_config = {"ip": "***********", "port": 1180}
    calidata_config = {"camera_matrix": [[1, 0, 0], [0, 1, 0], [0, 0, 1]], "distortion": [0, 0, 0, 0]}
    
    with open(os.path.join(runtime_dir, "ip_port.json"), 'w') as f:
        json.dump(ip_port_config, f)
    
    with open(os.path.join(runtime_dir, "calidata.json"), 'w') as f:
        json.dump(calidata_config, f)
    
    return {
        "environment": {
            "enabled": True,
            "cpp_source_directory": source_dir,
            "runtime_directory": runtime_dir,
            "required_files": test_files,
            "config_files": ["ip_port.json", "calidata.json"],
            "confirmation": {
                "timeout_seconds": 1,  # 短超时用于测试
                "default_action": "confirm",
                "show_rich_display": True
            },
            "validation": {
                "check_permissions": True,
                "check_file_integrity": True,
                "required_permissions": "755"
            }
        }
    }


def test_environment_manager_basic():
    """测试环境管理器基本功能"""
    print("=== 测试环境管理器基本功能 ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config = create_test_environment_config(temp_dir)
        env_manager = EnvironmentManager(config)
        
        # 测试源目录验证
        valid, error_msg = env_manager.validate_source_directory()
        print(f"✓ 源目录验证: {valid}")
        if not valid:
            print(f"  错误: {error_msg}")
        assert valid == True
        
        # 测试C++文件部署
        deployment_result = env_manager.deploy_cpp_files()
        print(f"✓ 文件部署结果: {deployment_result.success}")
        print(f"✓ 部署文件数: {len(deployment_result.deployed_files)}")
        print(f"✓ 部署时间: {deployment_result.deployment_time:.2f}秒")
        
        assert deployment_result.success == True
        assert len(deployment_result.deployed_files) == 5
        
        # 测试部署完整性验证
        integrity_result = env_manager.verify_deployment_integrity()
        print(f"✓ 完整性验证: 文件存在={integrity_result['all_files_present']}, 权限正确={integrity_result['permissions_correct']}")
        
        assert integrity_result["all_files_present"] == True
        assert integrity_result["permissions_correct"] == True
        
        print("✓ 环境管理器基本功能测试通过")
    
    return True


def test_config_confirmation():
    """测试配置确认功能"""
    print("\n=== 测试配置确认功能 ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config = create_test_environment_config(temp_dir)
        env_manager = EnvironmentManager(config)
        
        # 测试配置文件加载
        for config_file in ["ip_port.json", "calidata.json"]:
            success, content, error = env_manager.load_config_file(config_file)
            print(f"✓ 加载{config_file}: {success}")
            if success:
                print(f"  内容键: {list(content.keys())}")
            assert success == True
            
            # 测试配置验证
            valid, errors = env_manager.validate_config_file(config_file, content)
            print(f"✓ 验证{config_file}: {valid}")
            if not valid:
                print(f"  错误: {errors}")
            assert valid == True
        
        # 测试超时确认（模拟）
        with patch.object(env_manager.confirmation_manager, 'confirm_with_timeout') as mock_confirm:
            mock_confirm.return_value = "confirm"
            
            result = env_manager.confirm_config_file("ip_port.json")
            print(f"✓ 配置确认结果: {result}")
            assert result == "confirm"
        
        print("✓ 配置确认功能测试通过")
    
    return True


def test_environment_validation():
    """测试环境验证功能"""
    print("\n=== 测试环境验证功能 ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config = create_test_environment_config(temp_dir)
        env_manager = EnvironmentManager(config)
        
        # 先部署文件
        deployment_result = env_manager.deploy_cpp_files()
        assert deployment_result.success == True
        
        # 测试环境验证
        validation_result = env_manager.validate_environment()
        
        print(f"✓ 文件存在: {validation_result['all_files_exist']}")
        print(f"✓ 权限正确: {validation_result['permissions_correct']}")
        print(f"✓ 配置有效: {validation_result['configs_valid']}")
        print(f"✓ 验证摘要: {validation_result['summary']}")
        
        assert validation_result["all_files_exist"] == True
        assert validation_result["permissions_correct"] == True
        assert validation_result["configs_valid"] == True
        
        # 测试环境状态
        env_status = env_manager.get_environment_status()
        print(f"✓ 已部署文件数: {len(env_status['deployed_files'])}")
        print(f"✓ 缺失文件数: {len(env_status['missing_files'])}")
        
        assert len(env_status["deployed_files"]) == 5
        assert len(env_status["missing_files"]) == 0
        
        print("✓ 环境验证功能测试通过")
    
    return True


def test_environment_executor():
    """测试环境执行器"""
    print("\n=== 测试环境执行器 ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config = create_test_environment_config(temp_dir)
        executor = EnvironmentExecutor(config)
        
        # 测试配置验证
        valid, error_msg = executor.validate_environment_config()
        print(f"✓ 配置验证: {valid}")
        if not valid:
            print(f"  错误: {error_msg}")
        assert valid == True
        
        # Mock配置确认以避免交互
        with patch.object(executor.environment_manager, 'confirm_config_file') as mock_confirm:
            mock_confirm.return_value = "confirm"
            
            # 执行环境设置
            result = executor.execute_environment_setup(config)
            
            print(f"✓ 环境设置结果: {result.success}")
            print(f"✓ 执行消息: {result.message}")
            
            if result.data:
                deployment_data = result.data.get("deployment_result", {})
                print(f"✓ 部署文件数: {len(deployment_data.get('deployed_files', []))}")
                
                config_data = result.data.get("config_results", {})
                print(f"✓ 处理配置文件数: {len(config_data)}")
            
            assert result.success == True
            
        # 测试环境摘要
        summary = executor.get_environment_summary()
        print(f"✓ 环境摘要: 部署{summary['deployed_files_count']}个文件")
        
        assert summary["deployed_files_count"] == 5
        
        print("✓ 环境执行器测试通过")
    
    return True


def test_orchestrator_integration():
    """测试与TaskOrchestrator的集成"""
    print("\n=== 测试TaskOrchestrator集成 ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config = create_test_environment_config(temp_dir)
        orchestrator = TaskOrchestrator(config)
        
        # 检查环境管理执行器是否注册
        assert "ENV_SETUP" in orchestrator.stage_executors
        print("✓ 环境管理执行器已注册")
        
        # Mock配置确认
        with patch.object(orchestrator.stage_executors["ENV_SETUP"].__self__.environment_manager, 'confirm_config_file') as mock_confirm:
            mock_confirm.return_value = "confirm"
            
            # 执行环境设置阶段
            result = orchestrator.execute_stage("ENV_SETUP")
            
            print(f"✓ 阶段执行结果: {result.success}")
            print(f"✓ 执行消息: {result.message}")
            
            assert result.success == True
            
            # 验证状态转换
            assert orchestrator.current_state.value == "REMOTE_VALIDATION"
            print("✓ 状态正确转换到REMOTE_VALIDATION")
        
        print("✓ TaskOrchestrator集成测试通过")
    
    return True


def main():
    """主测试函数"""
    try:
        print("开始环境管理测试...")
        
        # 运行所有测试
        tests = [
            test_environment_manager_basic,
            test_config_confirmation,
            test_environment_validation,
            test_environment_executor,
            test_orchestrator_integration
        ]
        
        for test in tests:
            if not test():
                print(f"❌ 测试失败: {test.__name__}")
                return False
                
        print("\n🎉 所有环境管理测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
