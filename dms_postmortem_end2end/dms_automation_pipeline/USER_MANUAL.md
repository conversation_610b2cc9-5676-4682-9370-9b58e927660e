# 📚 DMS自动化分析与渲染平台 - 用户使用手册

## 🎯 概述

DMS自动化分析与渲染平台是一个端到端的自动化解决方案，专为简化DMS（Driver Monitoring System）视频分析和渲染流程而设计。本手册将指导您完成平台的安装、配置和使用。

## 📋 系统要求

### 硬件要求
- **CPU**: 4核心以上（推荐8核心）
- **内存**: 8GB以上（推荐16GB）
- **存储**: 50GB可用空间（推荐SSD）
- **网络**: 稳定的网络连接（如需远程功能）

### 软件要求
- **操作系统**: Linux (Ubuntu 18.04+, CentOS 7+)
- **Python**: 3.7或更高版本
- **FFmpeg**: 4.0或更高版本
- **SSH客户端**: 用于远程功能（可选）

## 🚀 快速开始

### 1. 环境准备

```bash
# 检查Python版本
python3 --version

# 安装FFmpeg
sudo apt update
sudo apt install ffmpeg

# 验证FFmpeg安装
ffmpeg -version
```

### 2. 安装依赖

```bash
# 安装Python依赖包
pip install paramiko rich jsonschema

# 或使用requirements.txt（如果提供）
pip install -r requirements.txt
```

### 3. 一键设置

```bash
# 进入项目目录
cd dms_automation_pipeline

# 执行一键设置
python quick_start.py --setup
```

### 4. 配置调整

编辑配置文件 `config/pipeline_config.json`：

```bash
nano config/pipeline_config.json
```

### 5. 运行测试

```bash
# 运行测试套件
python quick_start.py --run-tests

# 运行功能演示
python simple_demo.py
```

### 6. 启动平台

```bash
# 启动完整流水线
python quick_start.py --run

# 或使用主程序
python dms_automation_main.py --config config/pipeline_config.json
```

## ⚙️ 详细配置说明

### 配置文件结构

配置文件 `config/pipeline_config.json` 包含以下主要部分：

```json
{
  "video_processing": { ... },    // 视频处理配置
  "environment": { ... },         // 环境管理配置
  "remote": { ... },              // 远程验证配置
  "rendering": { ... },           // 渲染执行配置
  "logging": { ... }              // 日志配置
}
```

### 视频处理配置

```json
{
  "video_processing": {
    "enabled": true,                          // 是否启用视频处理
    "input_directory": "./input_videos/",     // 输入视频目录
    "output_directory": "./cropped_videos/",  // 输出视频目录
    "max_workers": 2,                         // 最大并行处理数
    "force_reprocess": false,                 // 是否强制重新处理
    "duplicate_detection": {
      "enabled": true,                        // 是否启用去重检测
      "strategy": "filename_based"            // 去重策略
    }
  }
}
```

**配置说明**：
- `max_workers`: 建议设置为CPU核心数的50-75%
- `force_reprocess`: 设为true时会重新处理已存在的文件
- `duplicate_detection`: 避免重复处理相同的视频文件

### 环境管理配置

```json
{
  "environment": {
    "enabled": true,                                    // 是否启用环境管理
    "cpp_source_directory": "../BYD_HKH_R_2.01.07.2025.07.08.4_x86/",  // C++源文件目录
    "runtime_directory": "./runtime_env/",             // 运行时目录
    "required_files": [                                // 必需文件列表
      "test_dms_internal_postmortem",
      "libtx_dms.so",
      "FaceDetection.ovm",
      "FaceKeypoints.ovm",
      "eye.ovm"
    ],
    "config_files": ["ip_port.json", "calidata.json"], // 配置文件列表
    "confirmation": {
      "timeout_seconds": 10,                           // 确认超时时间
      "default_action": "confirm"                      // 默认动作
    }
  }
}
```

**配置说明**：
- `cpp_source_directory`: 指向包含C++程序和模型文件的目录
- `required_files`: 必须存在的文件列表，缺失会导致部署失败
- `timeout_seconds`: 用户确认配置文件的等待时间

### 远程验证配置

```json
{
  "remote": {
    "enabled": false,                    // 是否启用远程功能
    "ssh": {
      "host": "***********",            // 远程主机地址
      "port": 22,                        // SSH端口
      "username": "user",                // SSH用户名
      "key_file": "~/.ssh/id_rsa",       // SSH密钥文件
      "timeout": 30,                     // 连接超时时间
      "retry_count": 3                   // 重试次数
    },
    "service": {
      "path": "/userfs/tx_dms_oax_test_tool_update",  // 远程服务路径
      "port": 1180,                                   // 服务端口
      "start_command": "cd /userfs && ./tx_dms_oax_test_tool_update",  // 启动命令
      "max_wait_time": 60                             // 最大等待时间
    },
    "model_sync": {
      "enabled": true,                               // 是否启用模型同步
      "local_model_path": "./runtime_env/",          // 本地模型路径
      "remote_model_path": "/userfs/models/",        // 远程模型路径
      "model_files": ["FaceDetection.ovm", "FaceKeypoints.ovm", "eye.ovm"],  // 模型文件列表
      "verify_md5": true                             // 是否验证MD5
    }
  }
}
```

**配置说明**：
- 远程功能是可选的，如不需要可设置 `enabled: false`
- SSH密钥认证比密码认证更安全，推荐使用
- `verify_md5`: 确保文件传输的完整性

### 渲染执行配置

```json
{
  "rendering": {
    "enabled": true,                     // 是否启用渲染
    "max_concurrent_tasks": 2,           // 最大并发任务数
    "task_timeout": 3600,                // 任务超时时间（秒）
    "output_directory": "./output/",     // 输出目录
    "batch_size": 10,                    // 批处理大小
    "min_success_rate": 0.8,             // 最小成功率
    "results": {
      "directory": "./results/",         // 结果目录
      "generate_reports": true,          // 是否生成报告
      "verify_outputs": true,            // 是否验证输出
      "collect_statistics": true         // 是否收集统计信息
    }
  }
}
```

**配置说明**：
- `max_concurrent_tasks`: 建议不超过CPU核心数
- `min_success_rate`: 低于此成功率会报告失败
- `task_timeout`: 单个任务的最大执行时间

## 🎮 使用指南

### 基本使用流程

1. **准备输入数据**
   ```bash
   # 将视频文件放入输入目录
   cp /path/to/videos/*.mp4 input_videos/
   ```

2. **检查配置**
   ```bash
   # 验证配置文件
   python -c "from core.config_manager import ConfigManager; cm = ConfigManager(); print(cm.validate_config('config/pipeline_config.json'))"
   ```

3. **执行处理**
   ```bash
   # 完整流水线
   python dms_automation_main.py --config config/pipeline_config.json
   
   # 从特定阶段开始
   python dms_automation_main.py --config config/pipeline_config.json --start-from ENV_SETUP
   
   # 调试模式
   python dms_automation_main.py --config config/pipeline_config.json --debug
   ```

4. **检查结果**
   ```bash
   # 查看输出文件
   ls -la output/
   
   # 查看处理报告
   ls -la results/
   ```

### 高级使用

#### 批量处理多个配置

```bash
# 处理多个配置文件
for config in configs/*.json; do
    echo "处理配置: $config"
    python dms_automation_main.py --config "$config"
done
```

#### 性能调优

```bash
# 运行性能分析
python performance_optimization_report.py

# 查看系统资源使用
htop
```

#### 远程部署

```bash
# 配置SSH密钥
ssh-keygen -t rsa -b 2048
ssh-copy-id user@remote-host

# 测试远程连接
ssh user@remote-host "echo 'Connection successful'"
```

## 📊 监控和日志

### 日志文件

- **主日志**: `logs/pipeline.log`
- **错误日志**: `logs/error.log`
- **调试日志**: `logs/debug.log`

### 日志级别

```json
{
  "logging": {
    "level": "INFO",                    // DEBUG, INFO, WARNING, ERROR
    "file": "./logs/pipeline.log",      // 日志文件路径
    "max_size": "10MB",                 // 最大文件大小
    "backup_count": 5                   // 备份文件数量
  }
}
```

### 进度监控

平台提供实时进度监控：

```bash
# 启用详细输出
export LOG_LEVEL=DEBUG
python dms_automation_main.py --config config/pipeline_config.json --debug
```

### 状态文件

平台会自动保存状态到 `.pipeline_state.json`，支持断点续传：

```json
{
  "current_state": "RENDERING",
  "timestamp": 1640995200.0,
  "last_completed_stage": "ENV_SETUP"
}
```

## 🔧 故障排除

### 常见问题

#### 1. 依赖包安装失败

**问题**: `pip install` 失败
**解决方案**:
```bash
# 升级pip
python -m pip install --upgrade pip

# 使用国内镜像
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ paramiko rich jsonschema

# 检查Python版本
python --version  # 需要3.7+
```

#### 2. FFmpeg未找到

**问题**: `ffmpeg: command not found`
**解决方案**:
```bash
# Ubuntu/Debian
sudo apt update && sudo apt install ffmpeg

# CentOS/RHEL
sudo yum install epel-release
sudo yum install ffmpeg

# 验证安装
ffmpeg -version
```

#### 3. 权限错误

**问题**: `Permission denied`
**解决方案**:
```bash
# 检查文件权限
ls -la runtime_env/

# 设置执行权限
chmod +x runtime_env/test_dms_internal_postmortem

# 检查目录权限
chmod 755 input_videos/ output/ results/
```

#### 4. SSH连接失败

**问题**: 远程连接失败
**解决方案**:
```bash
# 测试网络连通性
ping ***********

# 测试SSH连接
ssh -v user@***********

# 检查SSH密钥
ssh-add -l
```

#### 5. 内存不足

**问题**: `MemoryError` 或系统卡顿
**解决方案**:
```bash
# 减少并发数
# 在配置文件中设置：
"max_workers": 1,
"max_concurrent_tasks": 1

# 监控内存使用
free -h
```

#### 6. 磁盘空间不足

**问题**: `No space left on device`
**解决方案**:
```bash
# 检查磁盘空间
df -h

# 清理临时文件
rm -rf temp/
rm -rf logs/*.log.1

# 清理旧的输出文件
find output/ -name "*.mp4" -mtime +7 -delete
```

### 调试技巧

#### 启用详细日志

```bash
# 设置环境变量
export LOG_LEVEL=DEBUG

# 或在配置文件中设置
{
  "logging": {
    "level": "DEBUG"
  }
}
```

#### 单步调试

```bash
# 只运行特定阶段
python dms_automation_main.py --config config/pipeline_config.json --start-from VIDEO_PROCESSING --end-at VIDEO_PROCESSING
```

#### 检查配置

```bash
# 验证JSON格式
python -m json.tool config/pipeline_config.json

# 验证配置完整性
python quick_start.py --check-deps
```

### 性能优化建议

#### 1. 硬件优化

- **使用SSD**: 显著提升I/O性能
- **增加内存**: 减少磁盘交换
- **多核CPU**: 提高并行处理能力

#### 2. 配置优化

```json
{
  "video_processing": {
    "max_workers": 4,              // 设为CPU核心数
  },
  "rendering": {
    "max_concurrent_tasks": 4,     // 根据内存调整
    "batch_size": 20               // 增加批处理大小
  }
}
```

#### 3. 系统优化

```bash
# 增加文件描述符限制
ulimit -n 65536

# 优化内存管理
echo 'vm.swappiness=10' >> /etc/sysctl.conf
```

## 📞 技术支持

### 获取帮助

1. **查看文档**
   - README.md - 项目概述
   - PROJECT_COMPLETION_SUMMARY.md - 详细功能说明
   - DELIVERY_REPORT.md - 技术规格

2. **运行诊断**
   ```bash
   python quick_start.py --check-deps
   python quick_start.py --run-tests
   ```

3. **查看日志**
   ```bash
   tail -f logs/pipeline.log
   grep ERROR logs/pipeline.log
   ```

### 报告问题

报告问题时请提供：

1. **系统信息**
   ```bash
   uname -a
   python --version
   ffmpeg -version
   ```

2. **错误日志**
   ```bash
   tail -100 logs/pipeline.log
   ```

3. **配置文件**
   ```bash
   cat config/pipeline_config.json
   ```

4. **重现步骤**
   - 详细的操作步骤
   - 输入数据描述
   - 预期结果vs实际结果

---

## 📝 版本信息

- **版本**: v1.0.0
- **更新日期**: 2025年1月14日
- **兼容性**: Python 3.7+, FFmpeg 4.0+

---

🎯 **感谢使用DMS自动化分析与渲染平台！**
