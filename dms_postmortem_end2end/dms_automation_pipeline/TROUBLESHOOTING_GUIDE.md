# 🔧 DMS自动化分析与渲染平台 - 故障排除指南

## 📋 概述

本指南提供了DMS自动化分析与渲染平台常见问题的诊断和解决方案，帮助您快速定位和解决问题。

## 🚨 快速诊断

### 自动诊断工具

```bash
# 运行完整诊断
python quick_start.py --check-deps

# 检查系统状态
python -c "
import psutil
print(f'CPU: {psutil.cpu_percent()}%')
print(f'Memory: {psutil.virtual_memory().percent}%')
print(f'Disk: {psutil.disk_usage(\"/\").percent}%')
"

# 验证配置文件
python -c "
from core.config_manager import ConfigManager
cm = ConfigManager()
valid, error = cm.validate_config('config/pipeline_config.json')
print(f'Config valid: {valid}')
if not valid: print(f'Error: {error}')
"
```

### 日志检查

```bash
# 查看最新日志
tail -50 logs/pipeline.log

# 查看错误日志
grep -i error logs/pipeline.log

# 查看警告日志
grep -i warning logs/pipeline.log

# 实时监控日志
tail -f logs/pipeline.log
```

## ❌ 常见错误及解决方案

### 1. 安装和依赖问题

#### 错误：`ModuleNotFoundError: No module named 'paramiko'`

**原因**: Python依赖包未安装

**解决方案**:
```bash
# 安装缺失的包
pip install paramiko rich jsonschema

# 如果使用conda
conda install paramiko rich jsonschema

# 验证安装
python -c "import paramiko, rich, jsonschema; print('All modules imported successfully')"
```

#### 错误：`ffmpeg: command not found`

**原因**: FFmpeg未安装或不在PATH中

**解决方案**:
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install ffmpeg

# CentOS/RHEL
sudo yum install epel-release
sudo yum install ffmpeg

# macOS
brew install ffmpeg

# 验证安装
ffmpeg -version
which ffmpeg
```

#### 错误：`Python version 3.6 is not supported`

**原因**: Python版本过低

**解决方案**:
```bash
# 检查Python版本
python --version
python3 --version

# 安装Python 3.7+
# Ubuntu
sudo apt install python3.8

# 使用pyenv管理Python版本
curl https://pyenv.run | bash
pyenv install 3.8.10
pyenv global 3.8.10
```

### 2. 配置文件问题

#### 错误：`JSONDecodeError: Expecting ',' delimiter`

**原因**: JSON格式错误

**解决方案**:
```bash
# 验证JSON格式
python -m json.tool config/pipeline_config.json

# 常见JSON错误：
# 1. 缺少逗号
# 2. 多余的逗号
# 3. 未闭合的括号
# 4. 字符串未用双引号

# 使用在线JSON验证器
# 或使用文本编辑器的JSON语法检查
```

#### 错误：`Config validation failed: Missing required field`

**原因**: 配置文件缺少必需字段

**解决方案**:
```bash
# 重新生成默认配置
python quick_start.py --create-config

# 对比配置文件
diff config/pipeline_config.json config/pipeline_config.json.backup

# 检查必需字段
python -c "
from core.config_manager import ConfigManager
cm = ConfigManager()
schema = cm.load_schema()
print('Required fields:', schema.get('required', []))
"
```

### 3. 文件和权限问题

#### 错误：`PermissionError: [Errno 13] Permission denied`

**原因**: 文件或目录权限不足

**解决方案**:
```bash
# 检查文件权限
ls -la runtime_env/

# 设置正确权限
chmod +x runtime_env/test_dms_internal_postmortem
chmod 644 runtime_env/*.so
chmod 644 runtime_env/*.ovm

# 检查目录权限
chmod 755 input_videos/ output/ results/

# 检查所有者
sudo chown -R $USER:$USER ./
```

#### 错误：`FileNotFoundError: No such file or directory`

**原因**: 文件路径不存在

**解决方案**:
```bash
# 检查文件是否存在
ls -la /path/to/file

# 检查配置中的路径
grep -r "directory\|path" config/pipeline_config.json

# 创建缺失的目录
mkdir -p input_videos cropped_videos runtime_env output results logs

# 检查相对路径vs绝对路径
pwd
realpath ./input_videos
```

### 4. 网络和远程连接问题

#### 错误：`ssh: connect to host *********** port 22: Connection refused`

**原因**: SSH连接失败

**解决方案**:
```bash
# 检查网络连通性
ping ***********

# 检查端口是否开放
telnet *********** 22
# 或使用nmap
nmap -p 22 ***********

# 检查SSH服务状态（在远程主机上）
sudo systemctl status ssh
sudo systemctl start ssh

# 检查防火墙设置
sudo ufw status
sudo iptables -L
```

#### 错误：`Authentication failed`

**原因**: SSH认证失败

**解决方案**:
```bash
# 检查SSH密钥
ls -la ~/.ssh/
ssh-add -l

# 生成新的SSH密钥
ssh-keygen -t rsa -b 2048 -f ~/.ssh/id_rsa

# 复制公钥到远程主机
ssh-copy-id user@***********

# 测试SSH连接
ssh -v user@***********

# 检查SSH配置
cat ~/.ssh/config
```

### 5. 内存和性能问题

#### 错误：`MemoryError` 或系统卡顿

**原因**: 内存不足

**解决方案**:
```bash
# 检查内存使用
free -h
top
htop

# 减少并发数
# 在配置文件中设置：
{
  "video_processing": {
    "max_workers": 1
  },
  "rendering": {
    "max_concurrent_tasks": 1
  }
}

# 启用流式处理
{
  "performance": {
    "memory": {
      "streaming_mode": true,
      "max_memory_usage": "4GB"
    }
  }
}

# 清理内存
sudo sync
sudo echo 3 > /proc/sys/vm/drop_caches
```

#### 错误：`No space left on device`

**原因**: 磁盘空间不足

**解决方案**:
```bash
# 检查磁盘空间
df -h

# 查找大文件
du -sh * | sort -hr | head -10
find . -type f -size +100M

# 清理临时文件
rm -rf temp/
rm -rf logs/*.log.*

# 清理旧输出文件
find output/ -name "*.mp4" -mtime +7 -delete
find results/ -name "*.json" -mtime +30 -delete

# 压缩日志文件
gzip logs/*.log
```

### 6. 进程和服务问题

#### 错误：`Process already running`

**原因**: 进程已在运行

**解决方案**:
```bash
# 查找运行中的进程
ps aux | grep dms_automation
ps aux | grep python

# 终止进程
pkill -f dms_automation_main.py
kill -9 <PID>

# 检查端口占用
netstat -tulpn | grep :1180
lsof -i :1180

# 清理状态文件
rm -f .pipeline_state.json
```

#### 错误：`Service startup timeout`

**原因**: 服务启动超时

**解决方案**:
```bash
# 增加超时时间
{
  "remote": {
    "service": {
      "max_wait_time": 120
    }
  }
}

# 手动启动服务
ssh user@remote-host "cd /userfs && ./tx_dms_oax_test_tool_update &"

# 检查服务日志
ssh user@remote-host "tail -f /var/log/service.log"
```

## 🔍 高级调试技巧

### 启用详细日志

```bash
# 设置环境变量
export LOG_LEVEL=DEBUG

# 或在配置文件中设置
{
  "logging": {
    "level": "DEBUG",
    "console": {
      "enabled": true,
      "level": "DEBUG"
    }
  }
}

# 运行时启用调试
python dms_automation_main.py --config config/pipeline_config.json --debug
```

### 单步调试

```bash
# 只运行特定阶段
python dms_automation_main.py --start-from VIDEO_PROCESSING --end-at VIDEO_PROCESSING

# 跳过某个阶段
# 在配置文件中禁用：
{
  "remote": {
    "enabled": false
  }
}
```

### 性能分析

```bash
# 运行性能分析
python performance_optimization_report.py

# 监控系统资源
watch -n 1 'ps aux | grep python; free -h; df -h'

# 使用profiler
python -m cProfile -o profile.stats dms_automation_main.py
python -c "import pstats; p = pstats.Stats('profile.stats'); p.sort_stats('cumulative').print_stats(10)"
```

## 📊 监控和预防

### 健康检查脚本

```bash
#!/bin/bash
# health_check.sh

echo "=== DMS Platform Health Check ==="

# 检查Python环境
python --version || echo "❌ Python not found"

# 检查依赖
python -c "import paramiko, rich, jsonschema" 2>/dev/null && echo "✅ Dependencies OK" || echo "❌ Dependencies missing"

# 检查FFmpeg
ffmpeg -version >/dev/null 2>&1 && echo "✅ FFmpeg OK" || echo "❌ FFmpeg missing"

# 检查磁盘空间
DISK_USAGE=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -lt 90 ]; then
    echo "✅ Disk space OK ($DISK_USAGE%)"
else
    echo "❌ Disk space critical ($DISK_USAGE%)"
fi

# 检查内存
MEM_USAGE=$(free | grep Mem | awk '{printf "%.0f", $3/$2 * 100.0}')
if [ $MEM_USAGE -lt 85 ]; then
    echo "✅ Memory OK ($MEM_USAGE%)"
else
    echo "❌ Memory high ($MEM_USAGE%)"
fi

# 检查配置文件
python -c "from core.config_manager import ConfigManager; cm = ConfigManager(); valid, _ = cm.validate_config('config/pipeline_config.json'); print('✅ Config OK' if valid else '❌ Config invalid')"
```

### 自动清理脚本

```bash
#!/bin/bash
# cleanup.sh

echo "=== Cleaning up DMS Platform ==="

# 清理临时文件
rm -rf temp/*
echo "✅ Temp files cleaned"

# 压缩旧日志
find logs/ -name "*.log" -mtime +7 -exec gzip {} \;
echo "✅ Old logs compressed"

# 删除旧输出文件
find output/ -name "*.mp4" -mtime +30 -delete
echo "✅ Old output files deleted"

# 清理状态文件
if [ ! -f .pipeline_state.json ]; then
    rm -f .pipeline_state.json
    echo "✅ State file cleaned"
fi
```

## 📞 获取帮助

### 收集诊断信息

运行以下命令收集诊断信息：

```bash
#!/bin/bash
# collect_diagnostics.sh

echo "=== DMS Platform Diagnostics ===" > diagnostics.txt
echo "Date: $(date)" >> diagnostics.txt
echo "" >> diagnostics.txt

echo "=== System Info ===" >> diagnostics.txt
uname -a >> diagnostics.txt
python --version >> diagnostics.txt
ffmpeg -version 2>&1 | head -5 >> diagnostics.txt
echo "" >> diagnostics.txt

echo "=== Resource Usage ===" >> diagnostics.txt
free -h >> diagnostics.txt
df -h >> diagnostics.txt
echo "" >> diagnostics.txt

echo "=== Process Info ===" >> diagnostics.txt
ps aux | grep -E "(python|ffmpeg)" >> diagnostics.txt
echo "" >> diagnostics.txt

echo "=== Recent Logs ===" >> diagnostics.txt
tail -50 logs/pipeline.log >> diagnostics.txt
echo "" >> diagnostics.txt

echo "=== Config Validation ===" >> diagnostics.txt
python -c "from core.config_manager import ConfigManager; cm = ConfigManager(); valid, error = cm.validate_config('config/pipeline_config.json'); print(f'Valid: {valid}'); print(f'Error: {error}' if not valid else '')" >> diagnostics.txt

echo "Diagnostics saved to diagnostics.txt"
```

### 报告问题时请提供

1. **系统信息**: `uname -a`, `python --version`, `ffmpeg -version`
2. **错误日志**: 最近的错误日志和堆栈跟踪
3. **配置文件**: 当前使用的配置文件（隐藏敏感信息）
4. **重现步骤**: 详细的操作步骤
5. **环境信息**: 操作系统、Python版本、依赖版本

### 联系支持

- 查看项目文档: `README.md`, `USER_MANUAL.md`
- 运行自诊断: `python quick_start.py --check-deps`
- 查看示例: `python simple_demo.py`

---

## 🎯 预防措施

### 定期维护

1. **每日**: 检查日志文件，监控系统资源
2. **每周**: 清理临时文件，压缩旧日志
3. **每月**: 更新依赖包，备份配置文件
4. **每季度**: 性能优化分析，系统升级

### 最佳实践

1. **备份重要文件**: 定期备份配置文件和重要数据
2. **监控资源使用**: 设置资源使用告警
3. **保持系统更新**: 定期更新操作系统和依赖包
4. **文档记录**: 记录配置变更和问题解决过程

---

🔧 **及时发现和解决问题是系统稳定运行的关键！**
