# DMS自动化流水线

## 项目概述

DMS自动化流水线是一个端到端的自动化DMS分析系统，旨在解决手动流程痛点，提高处理效率。

## 核心功能

- **视频预处理**: 自动视频切割、去重检测、批量处理
- **环境管理**: C++文件自动部署、配置文件交互确认
- **远程验证**: SSH连接管理、服务状态检测、模型文件同步
- **渲染引擎**: DMS分析执行、结果文件管理、临时文件清理

## 项目结构

```
dms_automation_pipeline/
├── dms_automation_main.py      # 主入口程序
├── core/                       # 核心模块
│   ├── task_orchestrator.py    # 任务编排器
│   ├── video_preprocessor.py   # 视频预处理器
│   ├── environment_manager.py  # 环境管理器
│   ├── remote_validator.py     # 远程验证器
│   └── rendering_engine.py     # 渲染引擎
├── config/                     # 配置文件
│   ├── pipeline_config_template.json
│   └── validation_schemas.json
├── utils/                      # 工具模块
│   ├── file_utils.py           # 文件工具
│   ├── validation_utils.py     # 验证工具
│   └── logger_config.py        # 日志配置
└── tests/                      # 测试模块
```

## 快速开始

### 安装依赖

```bash
pip install rich paramiko jsonschema opencv-python psutil pytest pytest-cov
```

### 基本使用

```bash
# 查看帮助
python dms_automation_main.py --help

# 验证配置
python dms_automation_main.py --config config.json --validate-only

# 干运行模式
python dms_automation_main.py --config config.json --dry-run

# 执行完整流水线
python dms_automation_main.py --config config.json
```

## 配置说明

配置文件采用JSON格式，包含以下主要部分：

- `video_processing`: 视频处理相关配置
- `environment`: 环境管理配置
- `remote`: 远程服务配置
- `rendering`: 渲染引擎配置

详细配置说明请参考 `config/pipeline_config_template.json`

## 开发指南

### 运行测试

```bash
# 运行所有测试
python -m pytest tests/ -v

# 运行测试并生成覆盖率报告
python -m pytest tests/ -v --cov=dms_automation_pipeline --cov-report=html
```

### 代码规范

- 遵循PEP 8代码风格
- 使用类型注解
- 编写完整的文档字符串
- 保持代码覆盖率 > 90%

## 版本信息

- **版本**: 1.0.0
- **作者**: DMS Automation Team
- **创建时间**: 2025-07-14

## 许可证

内部项目，仅供团队使用。
