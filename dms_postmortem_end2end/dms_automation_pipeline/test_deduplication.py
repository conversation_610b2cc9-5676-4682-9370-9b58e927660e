#!/usr/bin/env python3
"""
去重机制测试脚本

验证DeduplicationManager的功能
"""

import sys
import os
import tempfile
from pathlib import Path

# 添加父目录到Python路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

from dms_automation_pipeline.core.video_preprocessor import DeduplicationManager


def test_deduplication_basic():
    """测试基本去重功能"""
    print("=== 测试基本去重功能 ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # 创建去重管理器
        dedup = DeduplicationManager(temp_dir)
        
        # 第一次检查 - 应该需要处理
        path1, needs1 = dedup.check_and_get_output_path(
            "test_video.mkv", "00:04:57", "00:05:17", "1920:1080:0:0"
        )
        print(f"✓ 第一次检查: {needs1} (应该为True)")
        assert needs1 == True
        
        # 创建文件模拟处理完成
        os.makedirs(os.path.dirname(path1), exist_ok=True)
        with open(path1, 'w') as f:
            f.write("test content")
        
        # 第二次检查 - 应该跳过
        path2, needs2 = dedup.check_and_get_output_path(
            "test_video.mkv", "00:04:57", "00:05:17", "1920:1080:0:0"
        )
        print(f"✓ 第二次检查: {needs2} (应该为False)")
        assert needs2 == False
        assert path1 == path2
        
        print("✓ 基本去重功能测试通过")
    
    return True


def test_naming_pattern():
    """测试文件命名模式"""
    print("\n=== 测试文件命名模式 ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # 测试默认命名模式
        dedup = DeduplicationManager(temp_dir)
        
        path, _ = dedup.check_and_get_output_path(
            "/path/to/test_video.mkv", "00:04:57", "00:05:17", "1920:1080:0:0"
        )
        
        expected_filename = "test_video_000457-000517_roi_1920_1080_0_0.mp4"
        actual_filename = os.path.basename(path)
        
        print(f"✓ 生成的文件名: {actual_filename}")
        assert actual_filename == expected_filename
        
        # 测试自定义命名模式
        custom_pattern = "{base_name}_segment_{time_range}.mp4"
        dedup_custom = DeduplicationManager(temp_dir, custom_pattern)
        
        path_custom, _ = dedup_custom.check_and_get_output_path(
            "/path/to/test_video.mkv", "00:04:57", "00:05:17"
        )
        
        expected_custom = "test_video_segment_000457-000517.mp4"
        actual_custom = os.path.basename(path_custom)
        
        print(f"✓ 自定义模式文件名: {actual_custom}")
        assert actual_custom == expected_custom
        
        print("✓ 文件命名模式测试通过")
    
    return True


def test_batch_deduplication():
    """测试批量去重检测"""
    print("\n=== 测试批量去重检测 ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        dedup = DeduplicationManager(temp_dir)
        
        # 准备批量配置
        batch_config = [
            {
                "path": "video1.mkv",
                "time_ranges": [("00:04:57", "00:05:17"), ("00:15:10", "00:15:30")],
                "roi": "1920:1080:0:0"
            },
            {
                "path": "video2.mkv", 
                "time_ranges": [("00:01:00", "00:01:20")],
                "roi": "1280:720:0:0"
            }
        ]
        
        # 第一次检查 - 应该没有重复文件
        duplicates = dedup.get_duplicate_files(batch_config)
        print(f"✓ 初始重复文件数: {len(duplicates)} (应该为0)")
        assert len(duplicates) == 0
        
        # 创建一些文件模拟已处理
        for config in batch_config:
            video_path = config["path"]
            time_ranges = config["time_ranges"]
            roi = config.get("roi")
            
            for start_time, end_time in time_ranges[:1]:  # 只创建第一个片段
                output_path, _ = dedup.check_and_get_output_path(
                    video_path, start_time, end_time, roi
                )
                os.makedirs(os.path.dirname(output_path), exist_ok=True)
                with open(output_path, 'w') as f:
                    f.write("test content")
        
        # 再次检查 - 应该有重复文件
        duplicates = dedup.get_duplicate_files(batch_config)
        print(f"✓ 创建文件后重复文件数: {len(duplicates)} (应该为2)")
        assert len(duplicates) == 2
        
        print("✓ 批量去重检测测试通过")
    
    return True


def test_edge_cases():
    """测试边界情况"""
    print("\n=== 测试边界情况 ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        dedup = DeduplicationManager(temp_dir)
        
        # 测试无ROI的情况
        path1, needs1 = dedup.check_and_get_output_path(
            "test_video.mkv", "00:04:57", "00:05:17", None
        )
        expected_no_roi = "test_video_000457-000517_roi_none.mp4"
        actual_no_roi = os.path.basename(path1)
        print(f"✓ 无ROI文件名: {actual_no_roi}")
        assert actual_no_roi == expected_no_roi
        
        # 测试特殊字符处理
        path2, needs2 = dedup.check_and_get_output_path(
            "test-video_with.special.chars.mkv", "00:04:57", "00:05:17", "1920:1080:100:200"
        )
        expected_special = "test-video_with.special.chars_000457-000517_roi_1920_1080_100_200.mp4"
        actual_special = os.path.basename(path2)
        print(f"✓ 特殊字符文件名: {actual_special}")
        assert actual_special == expected_special
        
        print("✓ 边界情况测试通过")
    
    return True


def test_integration_with_config():
    """测试与配置的集成"""
    print("\n=== 测试与配置的集成 ===")
    
    # 模拟配置
    config = {
        "video_processing": {
            "deduplication": {
                "enabled": True,
                "force_reprocess": False,
                "naming_pattern": "custom_{base_name}_{time_range}.mp4"
            }
        }
    }
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # 使用配置中的命名模式
        dedup_config = config["video_processing"]["deduplication"]
        dedup = DeduplicationManager(temp_dir, dedup_config["naming_pattern"])
        
        path, needs = dedup.check_and_get_output_path(
            "test_video.mkv", "00:04:57", "00:05:17"
        )
        
        expected_config = "custom_test_video_000457-000517.mp4"
        actual_config = os.path.basename(path)
        
        print(f"✓ 配置模式文件名: {actual_config}")
        assert actual_config == expected_config
        
        print("✓ 配置集成测试通过")
    
    return True


def main():
    """主测试函数"""
    try:
        print("开始测试去重机制...")
        
        # 运行所有测试
        tests = [
            test_deduplication_basic,
            test_naming_pattern,
            test_batch_deduplication,
            test_edge_cases,
            test_integration_with_config
        ]
        
        for test in tests:
            if not test():
                print(f"❌ 测试失败: {test.__name__}")
                return False
                
        print("\n🎉 所有去重机制测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
