#!/usr/bin/env python3
"""
远程验证测试脚本

验证RemoteValidator和RemoteExecutor的功能
"""

import sys
import os
import tempfile
from unittest.mock import patch, MagicMock

# 添加父目录到Python路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

from dms_automation_pipeline.core.remote_validator import (
    RemoteValidator, SSHManager, SSHConnection, RemoteServiceManager, ModelSyncManager
)
from dms_automation_pipeline.core.remote_executor import RemoteExecutor
from dms_automation_pipeline.core.task_orchestrator import TaskOrchestrator


def create_test_remote_config() -> dict:
    """创建测试远程配置"""
    return {
        "remote": {
            "enabled": True,
            "ssh": {
                "host": "***********",
                "port": 22,
                "username": "testuser",
                "key_file": "~/.ssh/id_rsa",
                "timeout": 30,
                "retry_count": 3
            },
            "service": {
                "path": "/userfs/tx_dms_oax_test_tool_update",
                "port": 1180,
                "start_command": "cd /userfs && ./tx_dms_oax_test_tool_update",
                "check_interval": 5,
                "max_wait_time": 60
            },
            "model_sync": {
                "enabled": True,
                "local_model_path": "./runtime_env/",
                "remote_model_path": "/userfs/models/",
                "model_files": ["FaceDetection.ovm", "FaceKeypoints.ovm", "eye.ovm"],
                "verify_md5": True,
                "auto_upload": True
            }
        }
    }


def test_ssh_manager():
    """测试SSH管理器"""
    print("=== 测试SSH管理器 ===")
    
    config = create_test_remote_config()
    ssh_manager = SSHManager(config)
    
    # 测试配置验证
    valid, error_msg = ssh_manager.validate_ssh_config()
    print(f"✓ SSH配置验证: {valid}")
    if not valid:
        print(f"  错误: {error_msg}")
    
    # 注意：这里不测试实际连接，因为需要真实的SSH服务器
    # 在实际环境中，可以通过Mock来测试连接逻辑
    
    # 测试连通性测试
    connectivity = ssh_manager.test_connectivity()
    print(f"✓ 连通性测试完成: {connectivity['reachable']}")
    print(f"  响应时间: {connectivity['response_time']:.2f}ms")
    
    print("✓ SSH管理器测试通过")
    return True


def test_remote_validator_mock():
    """测试远程验证器（Mock版本）"""
    print("\n=== 测试远程验证器（Mock版本） ===")
    
    config = create_test_remote_config()
    validator = RemoteValidator(config)
    
    # 测试配置验证
    valid, error_msg = validator.validate_remote_config()
    print(f"✓ 远程配置验证: {valid}")
    if not valid:
        print(f"  错误: {error_msg}")
    assert valid == True
    
    # Mock SSH连接
    with patch.object(validator.ssh_manager, 'connect') as mock_connect:
        # 创建Mock SSH连接
        mock_ssh_connection = MagicMock(spec=SSHConnection)
        mock_ssh_connection.is_connected.return_value = True
        mock_ssh_connection.execute_command.return_value = {
            "return_code": 0,
            "stdout": "test output",
            "stderr": "",
            "success": True
        }
        
        # 设置连接结果
        from dms_automation_pipeline.core.remote_validator import SSHConnectionResult
        mock_result = SSHConnectionResult(
            success=True,
            message="Mock连接成功",
            connection=mock_ssh_connection
        )
        mock_result.connection_time = 1.0
        mock_connect.return_value = mock_result
        
        # 测试连接
        connection_result = validator.connect()
        print(f"✓ 连接结果: {connection_result['success']}")
        print(f"✓ 连接消息: {connection_result['message']}")
        
        assert connection_result["success"] == True
        assert validator.ssh_connection is not None
        assert validator.service_manager is not None
        assert validator.model_sync_manager is not None
        
        # 测试环境验证
        with patch.object(validator.service_manager, 'check_service_status') as mock_service_status, \
             patch.object(validator.model_sync_manager, 'compare_files') as mock_compare:
            
            mock_service_status.return_value = {
                "running": True,
                "status": "running",
                "message": "服务正在运行",
                "file_exists": True
            }
            
            mock_compare.return_value = {
                "file_name": "test.ovm",
                "needs_sync": False,
                "sync_reason": "文件已同步"
            }
            
            env_validation = validator.validate_remote_environment()
            print(f"✓ 环境验证: {env_validation.get('overall_success', False)}")
            print(f"✓ 验证消息: {env_validation.get('message', '')}")
            
            assert env_validation.get("overall_success", False) == True
        
        # 断开连接
        validator.disconnect()
        
    print("✓ 远程验证器测试通过")
    return True


def test_service_connectivity():
    """测试服务连通性"""
    print("\n=== 测试服务连通性 ===")
    
    # 测试本地回环地址（应该可达）
    result = RemoteValidator.test_service_connectivity("127.0.0.1", 22)
    print(f"✓ 本地SSH连通性: {result['reachable']}")
    print(f"  响应时间: {result['response_time']:.2f}ms")
    
    # 测试不存在的地址（应该不可达）
    result = RemoteValidator.test_service_connectivity("192.168.255.255", 12345, timeout=1)
    print(f"✓ 不存在地址连通性: {result['reachable']} (应该为False)")
    print(f"  响应时间: {result['response_time']:.2f}ms")
    
    print("✓ 服务连通性测试通过")
    return True


def test_remote_executor():
    """测试远程执行器"""
    print("\n=== 测试远程执行器 ===")
    
    config = create_test_remote_config()
    executor = RemoteExecutor(config)
    
    # 测试配置验证
    valid, error_msg = executor.validate_remote_config()
    print(f"✓ 配置验证: {valid}")
    if not valid:
        print(f"  错误: {error_msg}")
    assert valid == True
    
    # Mock远程验证器的各个方法
    with patch.object(executor.remote_validator, 'connect') as mock_connect, \
         patch.object(executor.remote_validator, 'validate_remote_environment') as mock_env_validate, \
         patch.object(executor.remote_validator, 'disconnect') as mock_disconnect:
        
        # 设置Mock返回值
        mock_connect.return_value = {
            "success": True,
            "message": "连接成功",
            "connection_time": 1.0
        }
        
        mock_env_validate.return_value = {
            "overall_success": True,
            "message": "环境验证通过",
            "issues": [],
            "service_status": {"running": True},
            "model_sync_status": {"enabled": True, "files_need_sync": 0}
        }
        
        # 执行远程验证
        result = executor.execute_remote_validation(config)
        
        print(f"✓ 远程验证结果: {result.success}")
        print(f"✓ 执行消息: {result.message}")
        
        if result.data:
            components = result.data.get("components_checked", {})
            print(f"✓ 检查的组件: {sum(components.values())}个")
        
        assert result.success == True
        
        # 验证disconnect被调用
        mock_disconnect.assert_called_once()
    
    # 测试远程摘要
    summary = executor.get_remote_summary()
    print(f"✓ 远程摘要: SSH={summary['ssh_host']}:{summary['ssh_port']}")
    print(f"  服务端口: {summary['service_port']}")
    print(f"  模型同步: {summary['model_sync_enabled']}")
    
    assert summary["enabled"] == True
    assert summary["ssh_host"] == "***********"
    assert summary["service_port"] == 1180
    
    print("✓ 远程执行器测试通过")
    return True


def test_orchestrator_integration():
    """测试与TaskOrchestrator的集成"""
    print("\n=== 测试TaskOrchestrator集成 ===")
    
    config = create_test_remote_config()
    orchestrator = TaskOrchestrator(config)
    
    # 检查远程验证执行器是否注册
    assert "REMOTE_VALIDATION" in orchestrator.stage_executors
    print("✓ 远程验证执行器已注册")
    
    # Mock远程验证执行
    with patch.object(orchestrator.stage_executors["REMOTE_VALIDATION"].__self__.remote_validator, 'connect') as mock_connect, \
         patch.object(orchestrator.stage_executors["REMOTE_VALIDATION"].__self__.remote_validator, 'validate_remote_environment') as mock_env_validate, \
         patch.object(orchestrator.stage_executors["REMOTE_VALIDATION"].__self__.remote_validator, 'disconnect') as mock_disconnect:
        
        # 设置Mock返回值
        mock_connect.return_value = {
            "success": True,
            "message": "连接成功",
            "connection_time": 1.0
        }
        
        mock_env_validate.return_value = {
            "overall_success": True,
            "message": "环境验证通过",
            "issues": [],
            "service_status": {"running": True},
            "model_sync_status": {"enabled": True, "files_need_sync": 0}
        }
        
        # 执行远程验证阶段
        result = orchestrator.execute_stage("REMOTE_VALIDATION")
        
        print(f"✓ 阶段执行结果: {result.success}")
        print(f"✓ 执行消息: {result.message}")
        
        assert result.success == True
        
        # 验证状态转换
        assert orchestrator.current_state.value == "RENDERING"
        print("✓ 状态正确转换到RENDERING")
    
    print("✓ TaskOrchestrator集成测试通过")
    return True


def main():
    """主测试函数"""
    try:
        print("开始远程验证测试...")
        
        # 运行所有测试
        tests = [
            test_ssh_manager,
            test_remote_validator_mock,
            test_service_connectivity,
            test_remote_executor,
            test_orchestrator_integration
        ]
        
        for test in tests:
            if not test():
                print(f"❌ 测试失败: {test.__name__}")
                return False
                
        print("\n🎉 所有远程验证测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
