# ⚙️ DMS自动化分析与渲染平台 - 配置指南

## 📋 概述

本文档详细说明了DMS自动化分析与渲染平台的所有配置选项，帮助您根据具体需求优化平台性能。

## 📁 配置文件结构

主配置文件 `config/pipeline_config.json` 采用JSON格式，包含以下主要部分：

```json
{
  "video_processing": { ... },    // 视频处理模块配置
  "environment": { ... },         // 环境管理模块配置
  "remote": { ... },              // 远程验证模块配置
  "rendering": { ... },           // 渲染执行模块配置
  "logging": { ... }              // 日志系统配置
}
```

## 🎬 视频处理模块配置

### 基本配置

```json
{
  "video_processing": {
    "enabled": true,                          // 是否启用视频处理模块
    "input_directory": "./input_videos/",     // 输入视频文件目录
    "output_directory": "./cropped_videos/",  // 输出视频文件目录
    "max_workers": 2,                         // 最大并行处理线程数
    "force_reprocess": false,                 // 是否强制重新处理已存在的文件
    "temp_directory": "./temp/",              // 临时文件目录
    "cleanup_temp": true                      // 是否自动清理临时文件
  }
}
```

### 高级配置

```json
{
  "video_processing": {
    "duplicate_detection": {
      "enabled": true,                        // 是否启用重复检测
      "strategy": "filename_based",           // 检测策略: filename_based, md5_based
      "ignore_extensions": [".tmp", ".part"]  // 忽略的文件扩展名
    },
    "processing_options": {
      "quality": "high",                      // 处理质量: low, medium, high
      "format": "mp4",                        // 输出格式
      "codec": "h264",                        // 视频编码器
      "bitrate": "2M"                         // 视频比特率
    },
    "error_handling": {
      "max_retries": 3,                       // 最大重试次数
      "retry_delay": 5,                       // 重试延迟（秒）
      "skip_on_error": false                  // 遇到错误是否跳过
    }
  }
}
```

### 配置说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `enabled` | boolean | true | 控制是否启用视频处理模块 |
| `max_workers` | integer | 2 | 并行处理线程数，建议设为CPU核心数的50-75% |
| `force_reprocess` | boolean | false | true时会重新处理已存在的输出文件 |
| `quality` | string | "high" | 处理质量，影响处理时间和输出质量 |

## 🔧 环境管理模块配置

### 基本配置

```json
{
  "environment": {
    "enabled": true,                                    // 是否启用环境管理模块
    "cpp_source_directory": "../BYD_HKH_R_2.01.07.2025.07.08.4_x86/",  // C++源文件目录
    "runtime_directory": "./runtime_env/",             // 运行时环境目录
    "backup_directory": "./backup/",                   // 备份目录
    "create_backup": true                               // 是否创建备份
  }
}
```

### 文件管理配置

```json
{
  "environment": {
    "required_files": [                                // 必需的文件列表
      "test_dms_internal_postmortem",                  // 主程序
      "libtx_dms.so",                                  // 动态库
      "FaceDetection.ovm",                             // 模型文件
      "FaceKeypoints.ovm",
      "eye.ovm"
    ],
    "config_files": ["ip_port.json", "calidata.json"], // 配置文件列表
    "optional_files": ["debug.conf", "log.conf"],      // 可选文件列表
    "file_permissions": {
      "executables": "0755",                           // 可执行文件权限
      "libraries": "0644",                             // 库文件权限
      "configs": "0644"                                // 配置文件权限
    }
  }
}
```

### 用户交互配置

```json
{
  "environment": {
    "confirmation": {
      "timeout_seconds": 10,                           // 用户确认超时时间
      "default_action": "confirm",                     // 超时默认动作: confirm, cancel
      "show_file_content": true,                       // 是否显示文件内容
      "allow_editing": true                            // 是否允许编辑
    },
    "validation": {
      "check_md5": true,                               // 是否检查MD5
      "verify_permissions": true,                      // 是否验证权限
      "test_execution": false                          // 是否测试执行
    }
  }
}
```

## 🌐 远程验证模块配置

### SSH连接配置

```json
{
  "remote": {
    "enabled": false,                    // 是否启用远程功能
    "ssh": {
      "host": "***********",            // 远程主机IP地址
      "port": 22,                        // SSH端口
      "username": "user",                // SSH用户名
      "password": null,                  // SSH密码（不推荐）
      "key_file": "~/.ssh/id_rsa",       // SSH私钥文件路径
      "passphrase": null,                // 私钥密码
      "timeout": 30,                     // 连接超时时间（秒）
      "retry_count": 3,                  // 连接重试次数
      "retry_delay": 5                   // 重试延迟（秒）
    }
  }
}
```

### 远程服务配置

```json
{
  "remote": {
    "service": {
      "path": "/userfs/tx_dms_oax_test_tool_update",          // 远程服务程序路径
      "port": 1180,                                           // 服务监听端口
      "start_command": "cd /userfs && ./tx_dms_oax_test_tool_update",  // 启动命令
      "stop_command": "pkill -f tx_dms_oax_test_tool_update", // 停止命令
      "check_interval": 5,                                    // 状态检查间隔（秒）
      "max_wait_time": 60,                                    // 最大等待时间（秒）
      "auto_start": true,                                     // 是否自动启动
      "auto_stop": false                                      // 是否自动停止
    }
  }
}
```

### 模型同步配置

```json
{
  "remote": {
    "model_sync": {
      "enabled": true,                               // 是否启用模型同步
      "local_model_path": "./runtime_env/",          // 本地模型文件路径
      "remote_model_path": "/userfs/models/",        // 远程模型文件路径
      "model_files": [                               // 需要同步的模型文件
        "FaceDetection.ovm",
        "FaceKeypoints.ovm", 
        "eye.ovm"
      ],
      "verify_md5": true,                            // 是否验证MD5校验和
      "auto_upload": true,                           // 是否自动上传
      "compression": false,                          // 是否压缩传输
      "parallel_transfer": true                      // 是否并行传输
    }
  }
}
```

## 🎨 渲染执行模块配置

### 任务管理配置

```json
{
  "rendering": {
    "enabled": true,                     // 是否启用渲染模块
    "max_concurrent_tasks": 2,           // 最大并发任务数
    "task_timeout": 3600,                // 单个任务超时时间（秒）
    "queue_size": 100,                   // 任务队列大小
    "priority_levels": 4                 // 优先级级别数
  }
}
```

### 批处理配置

```json
{
  "rendering": {
    "batch_processing": {
      "batch_size": 10,                  // 批处理大小
      "batch_timeout": 7200,             // 批处理超时时间（秒）
      "min_success_rate": 0.8,           // 最小成功率
      "retry_failed": true,              // 是否重试失败的任务
      "parallel_batches": false          // 是否并行处理批次
    }
  }
}
```

### 输出配置

```json
{
  "rendering": {
    "output": {
      "directory": "./output/",          // 输出目录
      "filename_pattern": "rendered_{original_name}_{timestamp}",  // 文件名模式
      "format": "mp4",                   // 输出格式
      "quality": "high",                 // 输出质量
      "compression": true                // 是否压缩输出
    }
  }
}
```

### 结果收集配置

```json
{
  "rendering": {
    "results": {
      "directory": "./results/",         // 结果目录
      "generate_reports": true,          // 是否生成报告
      "report_format": "json",           // 报告格式: json, html, csv
      "verify_outputs": true,            // 是否验证输出文件
      "collect_statistics": true,        // 是否收集统计信息
      "cleanup_temp": true               // 是否清理临时文件
    }
  }
}
```

## 📝 日志系统配置

### 基本日志配置

```json
{
  "logging": {
    "level": "INFO",                     // 日志级别: DEBUG, INFO, WARNING, ERROR, CRITICAL
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",  // 日志格式
    "date_format": "%Y-%m-%d %H:%M:%S"   // 时间格式
  }
}
```

### 文件日志配置

```json
{
  "logging": {
    "file": {
      "enabled": true,                   // 是否启用文件日志
      "path": "./logs/pipeline.log",     // 日志文件路径
      "max_size": "10MB",                // 最大文件大小
      "backup_count": 5,                 // 备份文件数量
      "encoding": "utf-8"                // 文件编码
    }
  }
}
```

### 控制台日志配置

```json
{
  "logging": {
    "console": {
      "enabled": true,                   // 是否启用控制台日志
      "level": "INFO",                   // 控制台日志级别
      "colored": true                    // 是否使用彩色输出
    }
  }
}
```

## 🎯 性能调优配置

### CPU优化

```json
{
  "performance": {
    "cpu": {
      "max_workers": 4,                  // 最大工作线程数
      "thread_pool_size": 8,             // 线程池大小
      "cpu_affinity": null,              // CPU亲和性设置
      "nice_level": 0                    // 进程优先级
    }
  }
}
```

### 内存优化

```json
{
  "performance": {
    "memory": {
      "max_memory_usage": "8GB",         // 最大内存使用量
      "buffer_size": "64MB",             // 缓冲区大小
      "gc_threshold": 0.8,               // 垃圾回收阈值
      "streaming_mode": true             // 是否启用流式处理
    }
  }
}
```

### I/O优化

```json
{
  "performance": {
    "io": {
      "async_io": true,                  // 是否启用异步I/O
      "buffer_size": "1MB",              // I/O缓冲区大小
      "concurrent_reads": 4,             // 并发读取数
      "concurrent_writes": 2             // 并发写入数
    }
  }
}
```

## 🔒 安全配置

### 访问控制

```json
{
  "security": {
    "access_control": {
      "allowed_users": ["user1", "user2"],  // 允许的用户列表
      "allowed_ips": ["***********/24"],    // 允许的IP范围
      "require_authentication": false       // 是否需要认证
    }
  }
}
```

### 文件安全

```json
{
  "security": {
    "file_security": {
      "verify_checksums": true,          // 是否验证校验和
      "quarantine_suspicious": true,     // 是否隔离可疑文件
      "max_file_size": "1GB",            // 最大文件大小
      "allowed_extensions": [".mp4", ".avi", ".mov"]  // 允许的文件扩展名
    }
  }
}
```

## 📊 监控配置

### 性能监控

```json
{
  "monitoring": {
    "performance": {
      "enabled": true,                   // 是否启用性能监控
      "interval": 30,                    // 监控间隔（秒）
      "metrics": ["cpu", "memory", "disk", "network"],  // 监控指标
      "alert_thresholds": {
        "cpu_usage": 90,                 // CPU使用率告警阈值
        "memory_usage": 85,              // 内存使用率告警阈值
        "disk_usage": 90                 // 磁盘使用率告警阈值
      }
    }
  }
}
```

### 健康检查

```json
{
  "monitoring": {
    "health_check": {
      "enabled": true,                   // 是否启用健康检查
      "interval": 60,                    // 检查间隔（秒）
      "endpoints": [                     // 检查端点
        "http://localhost:8080/health",
        "tcp://localhost:1180"
      ],
      "timeout": 10                      // 检查超时时间（秒）
    }
  }
}
```

## 🔧 环境变量配置

可以通过环境变量覆盖配置文件中的设置：

```bash
# 日志级别
export LOG_LEVEL=DEBUG

# 最大工作线程数
export MAX_WORKERS=8

# 输出目录
export OUTPUT_DIR=/custom/output/path

# 远程主机
export REMOTE_HOST=*************

# SSH用户名
export SSH_USERNAME=admin
```

## 📋 配置验证

### 自动验证

平台会自动验证配置文件的格式和内容：

```bash
# 验证配置文件
python -c "from core.config_manager import ConfigManager; cm = ConfigManager(); print(cm.validate_config('config/pipeline_config.json'))"
```

### 手动验证

```bash
# 检查JSON格式
python -m json.tool config/pipeline_config.json

# 验证文件路径
python quick_start.py --check-deps
```

## 🎯 配置模板

### 开发环境配置

```json
{
  "video_processing": {
    "enabled": true,
    "max_workers": 2,
    "force_reprocess": true
  },
  "environment": {
    "enabled": true,
    "confirmation": {
      "timeout_seconds": 5
    }
  },
  "remote": {
    "enabled": false
  },
  "rendering": {
    "enabled": true,
    "max_concurrent_tasks": 1
  },
  "logging": {
    "level": "DEBUG"
  }
}
```

### 生产环境配置

```json
{
  "video_processing": {
    "enabled": true,
    "max_workers": 8,
    "force_reprocess": false
  },
  "environment": {
    "enabled": true,
    "confirmation": {
      "timeout_seconds": 30
    }
  },
  "remote": {
    "enabled": true
  },
  "rendering": {
    "enabled": true,
    "max_concurrent_tasks": 4
  },
  "logging": {
    "level": "INFO"
  }
}
```

### 高性能配置

```json
{
  "video_processing": {
    "enabled": true,
    "max_workers": 16,
    "processing_options": {
      "quality": "medium"
    }
  },
  "rendering": {
    "enabled": true,
    "max_concurrent_tasks": 8,
    "batch_processing": {
      "batch_size": 50
    }
  },
  "performance": {
    "memory": {
      "streaming_mode": true
    },
    "io": {
      "async_io": true,
      "concurrent_reads": 8
    }
  }
}
```

---

## 📞 配置支持

如需配置帮助，请：

1. 查看 `USER_MANUAL.md` 中的故障排除部分
2. 运行 `python quick_start.py --check-deps` 进行诊断
3. 检查日志文件 `logs/pipeline.log`

---

🎯 **正确的配置是平台高效运行的关键！**
